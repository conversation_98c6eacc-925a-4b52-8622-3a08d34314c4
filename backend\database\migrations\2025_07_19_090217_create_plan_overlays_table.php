<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_overlays', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('level_id')->constrained('project_levels')->onDelete('cascade');
            $table->enum('overlay_type', ['architectural', 'structural', 'electrical', 'plumbing', 'dry_fire_services', 'hvac', 'wet_fire_services']);
            $table->string('file_path');
            $table->boolean('is_base_plan')->default(false);
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_overlays');
    }
};
