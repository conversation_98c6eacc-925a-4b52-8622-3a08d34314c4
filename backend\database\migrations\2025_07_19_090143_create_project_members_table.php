<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->enum('role', ['project_admin', 'service_provider', 'inspector', 'guest']);
            $table->enum('discipline', ['electrical', 'plumbing', 'dry_fire_services', 'hvac', 'wet_fire_services'])->nullable();
            $table->enum('status', ['invited', 'active', 'inactive'])->default('invited');
            $table->json('permissions')->nullable();
            $table->json('assigned_work_areas')->nullable();
            $table->timestamps();

            $table->unique(['project_id', 'user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_members');
    }
};
