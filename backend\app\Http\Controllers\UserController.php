<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Manufacturer;
use App\Models\FireStoppingSystem;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = auth()->user();

        // Super admin can see all users
        if ($user->isSuperAdmin()) {
            $users = User::paginate(15);
        } else {
            // Other users can only see users in their projects
            $users = User::whereHas('memberProjects', function ($query) use ($user) {
                $query->whereHas('members', function ($subQuery) use ($user) {
                    $subQuery->where('user_id', $user->id);
                });
            })->paginate(15);
        }

        return response()->json($users);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'company_name' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'user_type' => 'required|in:super_admin,project_admin,service_provider,inspector,guest',
            'discipline' => 'nullable|in:electrical,plumbing,dry_fire_services,hvac,wet_fire_services',
            'status' => 'required|in:active,inactive,pending',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::create([
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'company_name' => $request->company_name,
            'phone' => $request->phone,
            'user_type' => $request->user_type,
            'discipline' => $request->discipline,
            'status' => $request->status,
        ]);

        return response()->json([
            'message' => 'User created successfully',
            'user' => $user
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        return response()->json($user);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user)
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|required|string|max:255',
            'last_name' => 'sometimes|required|string|max:255',
            'email' => 'sometimes|required|string|email|max:255|unique:users,email,' . $user->id,
            'company_name' => 'nullable|string|max:255',
            'phone' => 'nullable|string|max:20',
            'user_type' => 'sometimes|required|in:super_admin,project_admin,service_provider,inspector,guest',
            'discipline' => 'nullable|in:electrical,plumbing,dry_fire_services,hvac,wet_fire_services',
            'status' => 'sometimes|required|in:active,inactive,pending',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user->update($validator->validated());

        return response()->json([
            'message' => 'User updated successfully',
            'user' => $user
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        $user->delete();

        return response()->json([
            'message' => 'User deleted successfully'
        ]);
    }

    /**
     * Get all users (Super Admin only).
     */
    public function getAllUsers()
    {
        $users = User::with(['adminProjects', 'memberProjects'])->paginate(15);
        return response()->json($users);
    }

    /**
     * Create user (Super Admin only).
     */
    public function createUser(Request $request)
    {
        return $this->store($request);
    }

    /**
     * Update user (Super Admin only).
     */
    public function updateUser(Request $request, User $user)
    {
        return $this->update($request, $user);
    }

    /**
     * Delete user (Super Admin only).
     */
    public function deleteUser(User $user)
    {
        return $this->destroy($user);
    }

    /**
     * Get manufacturers.
     */
    public function getManufacturers()
    {
        $manufacturers = Manufacturer::with('fireStoppingSystems')->get();
        return response()->json($manufacturers);
    }

    /**
     * Create manufacturer.
     */
    public function createManufacturer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'contact_person' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $manufacturer = Manufacturer::create($validator->validated());

        return response()->json([
            'message' => 'Manufacturer created successfully',
            'manufacturer' => $manufacturer
        ], 201);
    }

    /**
     * Update user profile.
     */
    public function updateProfile(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'sometimes|nullable|string|max:20',
            'company_name' => 'sometimes|nullable|string|max:255',
            'discipline' => 'sometimes|nullable|in:electrical,plumbing,dry_fire_services,hvac,wet_fire_services',
            'bio' => 'sometimes|nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user->update($validator->validated());

        return response()->json([
            'message' => 'Profile updated successfully',
            'user' => $user->fresh()
        ]);
    }

    /**
     * Change user password.
     */
    public function changePassword(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'current_password' => 'required|string',
            'new_password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check current password
        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'message' => 'Current password is incorrect'
            ], 422);
        }

        $user->update([
            'password' => Hash::make($request->new_password)
        ]);

        return response()->json([
            'message' => 'Password changed successfully'
        ]);
    }

    /**
     * Update notification settings.
     */
    public function updateNotificationSettings(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'email_notifications' => 'boolean',
            'push_notifications' => 'boolean',
            'project_updates' => 'boolean',
            'inspection_reminders' => 'boolean',
            'team_invitations' => 'boolean',
            'system_announcements' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Store notification settings in user preferences or separate table
        $user->update([
            'notification_settings' => json_encode($validator->validated())
        ]);

        return response()->json([
            'message' => 'Notification settings updated successfully'
        ]);
    }

    /**
     * Update security settings.
     */
    public function updateSecuritySettings(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'two_factor_enabled' => 'boolean',
            'login_alerts' => 'boolean',
            'session_timeout' => 'integer|min:15|max:480',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Store security settings in user preferences or separate table
        $user->update([
            'security_settings' => json_encode($validator->validated())
        ]);

        return response()->json([
            'message' => 'Security settings updated successfully'
        ]);
    }

    /**
     * Upload user avatar.
     */
    public function uploadAvatar(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'avatar' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048', // 2MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Delete old avatar if exists
        if ($user->avatar_url && Storage::exists($user->avatar_url)) {
            Storage::delete($user->avatar_url);
        }

        $path = $request->file('avatar')->store('avatars', 'public');

        $user->update([
            'avatar_url' => $path
        ]);

        return response()->json([
            'message' => 'Avatar uploaded successfully',
            'avatar_url' => Storage::url($path)
        ]);
    }

    /**
     * Delete user account.
     */
    public function deleteAccount(Request $request)
    {
        $user = auth()->user();

        $validator = Validator::make($request->all(), [
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check password
        if (!Hash::check($request->password, $user->password)) {
            return response()->json([
                'message' => 'Password is incorrect'
            ], 422);
        }

        // Delete avatar if exists
        if ($user->avatar_url && Storage::exists($user->avatar_url)) {
            Storage::delete($user->avatar_url);
        }

        $user->delete();

        return response()->json([
            'message' => 'Account deleted successfully'
        ]);
    }
}
