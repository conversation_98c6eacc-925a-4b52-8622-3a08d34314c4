<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Manufacturer extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'contact_person',
        'phone',
        'email',
    ];

    /**
     * Get fire stopping systems for this manufacturer.
     */
    public function fireStoppingSystems()
    {
        return $this->hasMany(FireStoppingSystem::class);
    }
}
