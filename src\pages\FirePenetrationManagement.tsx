import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { 
  ArrowLeft, 
  Plus, 
  Search, 
  Filter, 
  MapPin, 
  Camera, 
  FileText, 
  Eye,
  Edit,
  Trash2,
  Check<PERSON>ir<PERSON>,
  Clock,
  AlertTriangle,
  Upload
} from "lucide-react";
import { usePermissions, PERMISSIONS } from "@/hooks/usePermissions";
import PermissionGate from "@/components/PermissionGate";
import { firePenetrationsApi, projectsApi } from "@/api/connectaBuildApi";
import { useToast } from "@/hooks/use-toast";

interface FirePenetration {
  id: number;
  service_id: string;
  service_name: string;
  service_type: string;
  service_description: string;
  tag: string;
  team_contact: string;
  substrate_type: string;
  substrate_material: string;
  frl_rating: string;
  cast_in_slab: boolean;
  performance_solution: boolean;
  inspection_requested: boolean;
  coordinates_x: number;
  coordinates_y: number;
  status: string;
  work_area: {
    id: number;
    area_name: string;
    area_code: string;
  };
  service_provider: {
    id: number;
    first_name: string;
    last_name: string;
    company_name: string;
  } | null;
  inspector: {
    id: number;
    first_name: string;
    last_name: string;
    company_name: string;
  } | null;
  created_at: string;
  updated_at: string;
}

const FirePenetrationManagement = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { hasPermission, isServiceProvider, isInspector, user } = usePermissions();
  
  const [penetrations, setPenetrations] = useState<FirePenetration[]>([]);
  const [filteredPenetrations, setFilteredPenetrations] = useState<FirePenetration[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [serviceTypeFilter, setServiceTypeFilter] = useState("all");
  const [isAddPenetrationOpen, setIsAddPenetrationOpen] = useState(false);
  const [selectedPenetration, setSelectedPenetration] = useState<FirePenetration | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  const [newPenetration, setNewPenetration] = useState({
    service_id: "",
    service_name: "",
    service_type: "electrical",
    service_description: "",
    tag: "",
    team_contact: "",
    substrate_type: "wall",
    substrate_material: "concrete",
    frl_rating: "",
    cast_in_slab: false,
    performance_solution: false,
    work_area_id: "",
    coordinates_x: 0,
    coordinates_y: 0
  });

  useEffect(() => {
    if (projectId) {
      loadPenetrations();
    }
  }, [projectId]);

  useEffect(() => {
    filterPenetrations();
  }, [penetrations, searchTerm, statusFilter, serviceTypeFilter]);

  const loadPenetrations = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with real API call
      const mockPenetrations: FirePenetration[] = [
        {
          id: 1,
          service_id: "FP-001",
          service_name: "Main Electrical Feed",
          service_type: "electrical",
          service_description: "Primary electrical conduit through fire wall",
          tag: "EL-001",
          team_contact: "John Smith",
          substrate_type: "wall",
          substrate_material: "concrete",
          frl_rating: "120/120/120",
          cast_in_slab: false,
          performance_solution: false,
          inspection_requested: true,
          coordinates_x: 100.5,
          coordinates_y: 200.3,
          status: "ready_for_inspection",
          work_area: {
            id: 1,
            area_name: "Electrical Room",
            area_code: "WA-001"
          },
          service_provider: {
            id: 1,
            first_name: "John",
            last_name: "Smith",
            company_name: "Spark Electrical"
          },
          inspector: null,
          created_at: "2024-01-15T10:00:00Z",
          updated_at: "2024-01-16T14:30:00Z"
        },
        {
          id: 2,
          service_id: "FP-002",
          service_name: "Water Supply Line",
          service_type: "plumbing",
          service_description: "Main water supply penetration",
          tag: "PL-001",
          team_contact: "Mike Wilson",
          substrate_type: "floor",
          substrate_material: "concrete",
          frl_rating: "90/90/90",
          cast_in_slab: true,
          performance_solution: false,
          inspection_requested: false,
          coordinates_x: 150.2,
          coordinates_y: 180.7,
          status: "images_uploaded",
          work_area: {
            id: 2,
            area_name: "Kitchen Area",
            area_code: "WA-002"
          },
          service_provider: {
            id: 2,
            first_name: "Mike",
            last_name: "Wilson",
            company_name: "Flow Plumbing"
          },
          inspector: null,
          created_at: "2024-01-14T09:00:00Z",
          updated_at: "2024-01-15T16:45:00Z"
        }
      ];
      
      setPenetrations(mockPenetrations);
    } catch (error: any) {
      console.error('Error loading penetrations:', error);
      toast({
        title: "Error",
        description: "Failed to load fire penetrations. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const filterPenetrations = () => {
    let filtered = penetrations;

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(p => 
        p.service_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.service_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.tag.toLowerCase().includes(searchTerm.toLowerCase()) ||
        p.work_area.area_name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filter by status
    if (statusFilter !== "all") {
      filtered = filtered.filter(p => p.status === statusFilter);
    }

    // Filter by service type
    if (serviceTypeFilter !== "all") {
      filtered = filtered.filter(p => p.service_type === serviceTypeFilter);
    }

    // Filter by user permissions
    if (isServiceProvider() && user) {
      filtered = filtered.filter(p => p.service_provider?.id === user.id);
    }

    setFilteredPenetrations(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'labelled': return 'bg-blue-100 text-blue-800';
      case 'images_uploaded': return 'bg-yellow-100 text-yellow-800';
      case 'interim_pass': return 'bg-green-100 text-green-800';
      case 'ready_for_inspection': return 'bg-purple-100 text-purple-800';
      case 'amendments_required': return 'bg-red-100 text-red-800';
      case 'final_pass': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'final_pass':
      case 'interim_pass':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'ready_for_inspection':
        return <AlertTriangle className="w-4 h-4 text-purple-600" />;
      case 'amendments_required':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getServiceTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      electrical: "bg-yellow-100 text-yellow-800",
      plumbing: "bg-blue-100 text-blue-800",
      dry_fire_services: "bg-orange-100 text-orange-800",
      hvac: "bg-purple-100 text-purple-800",
      wet_fire_services: "bg-red-100 text-red-800"
    };
    return colors[type] || "bg-gray-100 text-gray-800";
  };

  const handleAddPenetration = async () => {
    try {
      // Create penetration using API
      const penetrationData = {
        ...newPenetration,
        project_id: Number(projectId),
        service_provider_id: user?.id,
        status: 'not_started'
      };
      
      // Mock API call - replace with real API
      const newId = Math.max(...penetrations.map(p => p.id)) + 1;
      const createdPenetration: FirePenetration = {
        id: newId,
        ...newPenetration,
        inspection_requested: false,
        work_area: {
          id: Number(newPenetration.work_area_id),
          area_name: "Work Area",
          area_code: "WA-XXX"
        },
        service_provider: user ? {
          id: user.id,
          first_name: user.first_name,
          last_name: user.last_name,
          company_name: user.company_name || ""
        } : null,
        inspector: null,
        status: 'not_started',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      
      setPenetrations([...penetrations, createdPenetration]);
      
      // Reset form
      setNewPenetration({
        service_id: "",
        service_name: "",
        service_type: "electrical",
        service_description: "",
        tag: "",
        team_contact: "",
        substrate_type: "wall",
        substrate_material: "concrete",
        frl_rating: "",
        cast_in_slab: false,
        performance_solution: false,
        work_area_id: "",
        coordinates_x: 0,
        coordinates_y: 0
      });
      
      setIsAddPenetrationOpen(false);
      
      toast({
        title: "Success",
        description: "Fire penetration added successfully!",
      });
    } catch (error: any) {
      console.error('Error adding penetration:', error);
      toast({
        title: "Error",
        description: "Failed to add fire penetration. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleStatusUpdate = async (penetrationId: number, newStatus: string) => {
    try {
      // Update status using API
      setPenetrations(prev => prev.map(p => 
        p.id === penetrationId 
          ? { ...p, status: newStatus, updated_at: new Date().toISOString() }
          : p
      ));
      
      toast({
        title: "Success",
        description: "Status updated successfully!",
      });
    } catch (error: any) {
      console.error('Error updating status:', error);
      toast({
        title: "Error",
        description: "Failed to update status. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-connecta-cyan"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-8 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate(`/projects/${projectId}`)}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Project
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-foreground">Fire Penetrations</h1>
              <p className="text-muted-foreground">Manage fire penetrations for this project</p>
            </div>
          </div>
          <PermissionGate permission={PERMISSIONS.CREATE_PENETRATIONS}>
            <Button onClick={() => setIsAddPenetrationOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Add Penetration
            </Button>
          </PermissionGate>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Penetrations</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{penetrations.length}</div>
              <p className="text-xs text-muted-foreground">
                All penetrations
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Ready for Inspection</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {penetrations.filter(p => p.status === 'ready_for_inspection').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Awaiting inspection
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {penetrations.filter(p => p.status === 'final_pass').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Final approval
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Amendments Required</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {penetrations.filter(p => p.status === 'amendments_required').length}
              </div>
              <p className="text-xs text-muted-foreground">
                Need attention
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search penetrations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="not_started">Not Started</SelectItem>
                  <SelectItem value="labelled">Labelled</SelectItem>
                  <SelectItem value="images_uploaded">Images Uploaded</SelectItem>
                  <SelectItem value="interim_pass">Interim Pass</SelectItem>
                  <SelectItem value="ready_for_inspection">Ready for Inspection</SelectItem>
                  <SelectItem value="amendments_required">Amendments Required</SelectItem>
                  <SelectItem value="final_pass">Final Pass</SelectItem>
                </SelectContent>
              </Select>
              <Select value={serviceTypeFilter} onValueChange={setServiceTypeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by service type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Service Types</SelectItem>
                  <SelectItem value="electrical">Electrical</SelectItem>
                  <SelectItem value="plumbing">Plumbing</SelectItem>
                  <SelectItem value="dry_fire_services">Dry Fire Services</SelectItem>
                  <SelectItem value="hvac">HVAC</SelectItem>
                  <SelectItem value="wet_fire_services">Wet Fire Services</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Penetrations Table */}
        <Card>
          <CardHeader>
            <CardTitle>Fire Penetrations</CardTitle>
            <CardDescription>
              {filteredPenetrations.length} of {penetrations.length} penetrations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Service ID</TableHead>
                  <TableHead>Service Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Work Area</TableHead>
                  <TableHead>Service Provider</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPenetrations.map((penetration) => (
                  <TableRow key={penetration.id}>
                    <TableCell className="font-medium">{penetration.service_id}</TableCell>
                    <TableCell>{penetration.service_name}</TableCell>
                    <TableCell>
                      <Badge className={getServiceTypeColor(penetration.service_type)}>
                        {penetration.service_type.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{penetration.work_area.area_name}</TableCell>
                    <TableCell>
                      {penetration.service_provider 
                        ? `${penetration.service_provider.first_name} ${penetration.service_provider.last_name}`
                        : 'Unassigned'
                      }
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(penetration.status)}
                        <Badge className={getStatusColor(penetration.status)}>
                          {penetration.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          onClick={() => {
                            setSelectedPenetration(penetration);
                            setIsViewModalOpen(true);
                          }}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        
                        <PermissionGate 
                          permission={PERMISSIONS.UPDATE_PENETRATIONS}
                          context={{ penetration }}
                        >
                          <Button variant="ghost" size="sm">
                            <Edit className="w-4 h-4" />
                          </Button>
                        </PermissionGate>
                        
                        {isInspector() && penetration.status === 'ready_for_inspection' && (
                          <PermissionGate permission={PERMISSIONS.PERFORM_INSPECTIONS}>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleStatusUpdate(penetration.id, 'final_pass')}
                              className="text-green-600"
                            >
                              <CheckCircle className="w-4 h-4" />
                            </Button>
                          </PermissionGate>
                        )}
                        
                        <PermissionGate 
                          permission={PERMISSIONS.DELETE_PENETRATIONS}
                          context={{ penetration }}
                        >
                          <Button 
                            variant="ghost" 
                            size="sm"
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </PermissionGate>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Add Penetration Dialog */}
        <Dialog open={isAddPenetrationOpen} onOpenChange={setIsAddPenetrationOpen}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Fire Penetration</DialogTitle>
            </DialogHeader>
            <div className="grid grid-cols-2 gap-4 py-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Service ID</label>
                <Input
                  value={newPenetration.service_id}
                  onChange={(e) => setNewPenetration(prev => ({ ...prev, service_id: e.target.value }))}
                  placeholder="FP-001"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Service Name</label>
                <Input
                  value={newPenetration.service_name}
                  onChange={(e) => setNewPenetration(prev => ({ ...prev, service_name: e.target.value }))}
                  placeholder="Main electrical feed"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Service Type</label>
                <Select 
                  value={newPenetration.service_type} 
                  onValueChange={(value) => setNewPenetration(prev => ({ ...prev, service_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="electrical">Electrical</SelectItem>
                    <SelectItem value="plumbing">Plumbing</SelectItem>
                    <SelectItem value="dry_fire_services">Dry Fire Services</SelectItem>
                    <SelectItem value="hvac">HVAC</SelectItem>
                    <SelectItem value="wet_fire_services">Wet Fire Services</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Tag</label>
                <Input
                  value={newPenetration.tag}
                  onChange={(e) => setNewPenetration(prev => ({ ...prev, tag: e.target.value }))}
                  placeholder="EL-001"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Substrate Type</label>
                <Select 
                  value={newPenetration.substrate_type} 
                  onValueChange={(value) => setNewPenetration(prev => ({ ...prev, substrate_type: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="wall">Wall</SelectItem>
                    <SelectItem value="floor">Floor</SelectItem>
                    <SelectItem value="ceiling">Ceiling</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Substrate Material</label>
                <Select 
                  value={newPenetration.substrate_material} 
                  onValueChange={(value) => setNewPenetration(prev => ({ ...prev, substrate_material: value }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="concrete">Concrete</SelectItem>
                    <SelectItem value="hebel">Hebel</SelectItem>
                    <SelectItem value="steel">Steel</SelectItem>
                    <SelectItem value="timber">Timber</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="col-span-2 space-y-2">
                <label className="text-sm font-medium">Service Description</label>
                <Textarea
                  value={newPenetration.service_description}
                  onChange={(e) => setNewPenetration(prev => ({ ...prev, service_description: e.target.value }))}
                  placeholder="Describe the service penetration..."
                  rows={3}
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2">
              <Button variant="outline" onClick={() => setIsAddPenetrationOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddPenetration}>
                Add Penetration
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* View Penetration Dialog */}
        <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Fire Penetration Details</DialogTitle>
            </DialogHeader>
            {selectedPenetration && (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Service ID</label>
                      <p className="text-sm font-medium">{selectedPenetration.service_id}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Service Name</label>
                      <p className="text-sm">{selectedPenetration.service_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Service Type</label>
                      <Badge className={getServiceTypeColor(selectedPenetration.service_type)}>
                        {selectedPenetration.service_type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Work Area</label>
                      <p className="text-sm">{selectedPenetration.work_area.area_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Status</label>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(selectedPenetration.status)}
                        <Badge className={getStatusColor(selectedPenetration.status)}>
                          {selectedPenetration.status.replace('_', ' ')}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Tag</label>
                      <p className="text-sm">{selectedPenetration.tag}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Substrate</label>
                      <p className="text-sm capitalize">
                        {selectedPenetration.substrate_material} {selectedPenetration.substrate_type}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">FRL Rating</label>
                      <p className="text-sm">{selectedPenetration.frl_rating}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Service Provider</label>
                      <p className="text-sm">
                        {selectedPenetration.service_provider 
                          ? `${selectedPenetration.service_provider.first_name} ${selectedPenetration.service_provider.last_name}`
                          : 'Unassigned'
                        }
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                      <p className="text-sm">{new Date(selectedPenetration.updated_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
                
                {selectedPenetration.service_description && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Description</label>
                    <p className="text-sm mt-1">{selectedPenetration.service_description}</p>
                  </div>
                )}

                <div className="flex justify-between">
                  <div className="flex space-x-2">
                    <PermissionGate permission={PERMISSIONS.UPLOAD_FILES}>
                      <Button variant="outline">
                        <Camera className="w-4 h-4 mr-2" />
                        Add Photos
                      </Button>
                    </PermissionGate>
                    <Button variant="outline">
                      <FileText className="w-4 h-4 mr-2" />
                      View Documents
                    </Button>
                  </div>
                  
                  {isInspector() && selectedPenetration.status === 'ready_for_inspection' && (
                    <div className="flex space-x-2">
                      <Button 
                        variant="outline"
                        onClick={() => handleStatusUpdate(selectedPenetration.id, 'amendments_required')}
                        className="text-red-600"
                      >
                        Request Amendments
                      </Button>
                      <Button 
                        onClick={() => handleStatusUpdate(selectedPenetration.id, 'final_pass')}
                        className="text-green-600"
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        Approve
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
};

export default FirePenetrationManagement;
