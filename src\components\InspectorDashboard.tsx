import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  FileText,
  Eye,
  XCircle,
  Calendar
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";

const InspectorDashboard = () => {
  const { user } = usePermissions();
  const [stats, setStats] = useState({
    assignedProjects: 5,
    pendingInspections: 12,
    completedInspections: 89,
    amendmentsRequired: 3,
    approvalRate: 94
  });

  const pendingInspections = [
    {
      id: "FP-045",
      project: "Tower Complex A",
      workArea: "Level 8 - Electrical Room",
      serviceProvider: "Spark Electrical",
      discipline: "electrical",
      requestedDate: "2024-01-15",
      priority: "high",
      daysWaiting: 2
    },
    {
      id: "FP-046",
      project: "Office Building B", 
      workArea: "Level 2 - Kitchen",
      serviceProvider: "Flow Plumbing",
      discipline: "plumbing",
      requestedDate: "2024-01-14",
      priority: "medium",
      daysWaiting: 3
    },
    {
      id: "FP-047",
      project: "Residential Complex",
      workArea: "Level 5 - HVAC Plant",
      serviceProvider: "Air Solutions",
      discipline: "hvac",
      requestedDate: "2024-01-13",
      priority: "low",
      daysWaiting: 4
    }
  ];

  const recentInspections = [
    {
      id: "FP-042",
      project: "Tower Complex A",
      result: "approved",
      date: "2024-01-12",
      serviceProvider: "Spark Electrical"
    },
    {
      id: "FP-043",
      project: "Office Building B",
      result: "amendments_required",
      date: "2024-01-11",
      serviceProvider: "Flow Plumbing"
    },
    {
      id: "FP-044",
      project: "Residential Complex",
      result: "approved",
      date: "2024-01-10",
      serviceProvider: "Air Solutions"
    }
  ];

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getResultColor = (result: string) => {
    switch (result) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'amendments_required': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getResultIcon = (result: string) => {
    switch (result) {
      case 'approved': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'amendments_required': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  const getDisciplineColor = (discipline: string) => {
    const colors: { [key: string]: string } = {
      electrical: "bg-yellow-100 text-yellow-800",
      plumbing: "bg-blue-100 text-blue-800",
      dry_fire_services: "bg-orange-100 text-orange-800",
      hvac: "bg-purple-100 text-purple-800",
      wet_fire_services: "bg-red-100 text-red-800"
    };
    return colors[discipline] || "bg-gray-100 text-gray-800";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Inspector Dashboard
          </h1>
          <p className="text-muted-foreground">
            Welcome back, {user?.first_name}! You have {stats.pendingInspections} inspections pending.
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Calendar className="w-4 h-4 mr-2" />
            Schedule Inspection
          </Button>
          <Button>
            <Eye className="w-4 h-4 mr-2" />
            Start Inspection
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned Projects</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.assignedProjects}</div>
            <p className="text-xs text-muted-foreground">
              Active projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Inspections</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingInspections}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedInspections}</div>
            <p className="text-xs text-muted-foreground">
              This month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Amendments</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.amendmentsRequired}</div>
            <p className="text-xs text-muted-foreground">
              Require changes
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approval Rate</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.approvalRate}%</div>
            <p className="text-xs text-muted-foreground">
              First-time approvals
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Pending Inspections */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Clock className="w-5 h-5 mr-2" />
            Pending Inspections
          </CardTitle>
          <CardDescription>Fire penetrations awaiting your inspection</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {pendingInspections.map((inspection) => (
              <div key={inspection.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex flex-col items-center">
                    <Badge className={getPriorityColor(inspection.priority)}>
                      {inspection.priority}
                    </Badge>
                    <span className="text-xs text-muted-foreground mt-1">
                      {inspection.daysWaiting}d waiting
                    </span>
                  </div>
                  <div>
                    <p className="font-medium">{inspection.id}</p>
                    <p className="text-sm text-muted-foreground">{inspection.project}</p>
                    <p className="text-xs text-muted-foreground">{inspection.workArea}</p>
                    <p className="text-xs text-muted-foreground">by {inspection.serviceProvider}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getDisciplineColor(inspection.discipline)}>
                    {inspection.discipline}
                  </Badge>
                  <Button size="sm">
                    <Eye className="w-4 h-4 mr-1" />
                    Inspect
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Inspections */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Inspection Results</CardTitle>
          <CardDescription>Your latest inspection outcomes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentInspections.map((inspection) => (
              <div key={inspection.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  {getResultIcon(inspection.result)}
                  <div>
                    <p className="font-medium">{inspection.id}</p>
                    <p className="text-sm text-muted-foreground">{inspection.project}</p>
                    <p className="text-xs text-muted-foreground">by {inspection.serviceProvider}</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className={getResultColor(inspection.result)}>
                    {inspection.result.replace('_', ' ')}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">
                    {inspection.date}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Schedule Inspection</CardTitle>
            <CardDescription>Plan upcoming inspections</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              <Calendar className="w-4 h-4 mr-2" />
              Schedule
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Inspection Checklist</CardTitle>
            <CardDescription>View inspection criteria</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              <Shield className="w-4 h-4 mr-2" />
              View Checklist
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Generate Report</CardTitle>
            <CardDescription>Create inspection summary</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              <FileText className="w-4 h-4 mr-2" />
              Create Report
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default InspectorDashboard;
