<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Project;
use App\Models\ProjectLevel;
use App\Models\WorkArea;
use App\Models\FirePenetration;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test users
        $superAdmin = User::create([
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'ConnectaBuild',
            'phone' => '+61 ***********',
            'user_type' => 'super_admin',
            'discipline' => null,
            'status' => 'active',
        ]);

        $projectAdmin = User::create([
            'first_name' => 'John',
            'last_name' => 'Manager',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'Project Management Co',
            'phone' => '+61 ***********',
            'user_type' => 'project_admin',
            'discipline' => null,
            'status' => 'active',
        ]);

        $serviceProvider = User::create([
            'first_name' => 'Mike',
            'last_name' => 'Electrician',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'Spark Electrical',
            'phone' => '+61 ***********',
            'user_type' => 'service_provider',
            'discipline' => 'electrical',
            'status' => 'active',
        ]);

        $inspector = User::create([
            'first_name' => 'Sarah',
            'last_name' => 'Inspector',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'Quality Inspections',
            'phone' => '+61 ***********',
            'user_type' => 'inspector',
            'discipline' => null,
            'status' => 'active',
        ]);

        // Create test project
        $project = Project::create([
            'project_code' => 'PROJ001',
            'project_name' => 'Tower Complex A',
            'description' => 'High-rise residential tower with commercial ground floor',
            'address' => '123 Collins Street',
            'city' => 'Melbourne',
            'state' => 'VIC',
            'postal_code' => '3000',
            'country' => 'Australia',
            'project_admin_id' => $projectAdmin->id,
            'client_name' => 'ABC Development',
            'client_email' => '<EMAIL>',
            'client_phone' => '+61 3 9000 0000',
            'contractor_name' => 'XYZ Construction',
            'contractor_email' => '<EMAIL>',
            'contractor_phone' => '+61 3 9000 0001',
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
            'status' => 'published',
        ]);

        // Add project members
        $project->members()->attach($serviceProvider->id, [
            'role' => 'service_provider',
            'discipline' => 'electrical',
            'status' => 'active',
            'permissions' => json_encode(['create_penetrations', 'update_own_penetrations']),
            'assigned_work_areas' => json_encode([]),
        ]);

        $project->members()->attach($inspector->id, [
            'role' => 'inspector',
            'discipline' => null,
            'status' => 'active',
            'permissions' => json_encode(['perform_inspections', 'approve_penetrations']),
            'assigned_work_areas' => json_encode([]),
        ]);

        // Create project levels
        $groundLevel = ProjectLevel::create([
            'project_id' => $project->id,
            'level_name' => 'Ground Floor',
            'level_number' => 0,
            'level_type' => 'ground',
        ]);

        $level1 = ProjectLevel::create([
            'project_id' => $project->id,
            'level_name' => 'Level 1',
            'level_number' => 1,
            'level_type' => 'floor',
        ]);

        // Create work areas
        $workArea1 = WorkArea::create([
            'project_id' => $project->id,
            'level_id' => $groundLevel->id,
            'area_code' => 'WA-001',
            'area_name' => 'Electrical Room',
            'area_type' => 'wall',
            'category' => 'mechanical',
            'frl_rating' => '120/120/120',
            'substrate_type' => 'wall',
            'substrate_material' => 'concrete',
            'polygon_coordinates' => json_encode([]),
            'display_color' => '#1976D2',
            'line_weight' => 2,
        ]);

        $workArea2 = WorkArea::create([
            'project_id' => $project->id,
            'level_id' => $level1->id,
            'area_code' => 'WA-002',
            'area_name' => 'Apartment 101',
            'area_type' => 'wall',
            'category' => 'apartment',
            'frl_rating' => '90/90/90',
            'substrate_type' => 'wall',
            'substrate_material' => 'concrete',
            'polygon_coordinates' => json_encode([]),
            'display_color' => '#4CAF50',
            'line_weight' => 2,
        ]);

        // Create fire penetrations
        FirePenetration::create([
            'project_id' => $project->id,
            'work_area_id' => $workArea1->id,
            'service_provider_id' => $serviceProvider->id,
            'service_id' => 'FP-001',
            'service_name' => 'Main Electrical Feed',
            'service_type' => 'electrical',
            'service_description' => 'Primary electrical conduit through fire wall',
            'tag' => 'EL-001',
            'team_contact' => 'Mike Electrician',
            'substrate_type' => 'wall',
            'substrate_material' => 'concrete',
            'frl_rating' => '120/120/120',
            'cast_in_slab' => false,
            'performance_solution' => false,
            'coordinates_x' => 100.5,
            'coordinates_y' => 200.3,
            'status' => 'ready_for_inspection',
            'inspection_requested' => true,
        ]);

        FirePenetration::create([
            'project_id' => $project->id,
            'work_area_id' => $workArea2->id,
            'service_provider_id' => $serviceProvider->id,
            'service_id' => 'FP-002',
            'service_name' => 'Apartment Power Supply',
            'service_type' => 'electrical',
            'service_description' => 'Electrical supply to apartment unit',
            'tag' => 'EL-002',
            'team_contact' => 'Mike Electrician',
            'substrate_type' => 'wall',
            'substrate_material' => 'concrete',
            'frl_rating' => '90/90/90',
            'cast_in_slab' => false,
            'performance_solution' => false,
            'coordinates_x' => 150.2,
            'coordinates_y' => 180.7,
            'status' => 'images_uploaded',
            'inspection_requested' => false,
        ]);

        $this->command->info('Test data seeded successfully!');
        $this->command->info('Login credentials:');
        $this->command->info('Super Admin: <EMAIL> / password123');
        $this->command->info('Project Admin: <EMAIL> / password123');
        $this->command->info('Service Provider: <EMAIL> / password123');
        $this->command->info('Inspector: <EMAIL> / password123');
    }
}
