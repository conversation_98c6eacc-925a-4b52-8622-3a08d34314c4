import { Home, Settings, LogOut, Users, FolderOpen, RotateCcw, Building, Shield, BarChart3 } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { resetDatabase } from "@/api/mockApi";
import { toast } from "sonner";
import { usePermissions } from "@/hooks/usePermissions";
import { useAuth } from "@/contexts/AuthContext";
import PermissionGate from "@/components/PermissionGate";
import connectaIcon from "@/assets/connecta-logo-icon.png";

const Sidebar = () => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const { isSuperAdmin, isProjectAdmin, isServiceProvider, isInspector } = usePermissions();

  const handleResetDatabase = () => {
    if (confirm("Are you sure you want to reset the database? This will restore all data to the original state and cannot be undone.")) {
      resetDatabase();
      toast.success("Database reset successfully! Please refresh the page to see changes.");
      setTimeout(() => {
        window.location.reload();
      }, 1000);
    }
  };

  // Get navigation items based on user role
  const getNavigationItems = () => {
    if (isSuperAdmin()) {
      return [
        {
          label: "Dashboard",
          href: "/admin?tab=dashboard",
          icon: BarChart3,
          active: location.pathname === "/admin" && (new URLSearchParams(location.search).get("tab") === "dashboard" || !new URLSearchParams(location.search).get("tab")),
        },
        {
          label: "Projects",
          href: "/admin?tab=projects",
          icon: FolderOpen,
          active: location.pathname === "/admin" && new URLSearchParams(location.search).get("tab") === "projects",
        },
        {
          label: "Users",
          href: "/admin?tab=users",
          icon: Users,
          active: location.pathname === "/admin" && new URLSearchParams(location.search).get("tab") === "users",
        },
        {
          label: "Manufacturers",
          href: "/admin?tab=manufacturers",
          icon: Building,
          active: location.pathname === "/admin" && new URLSearchParams(location.search).get("tab") === "manufacturers",
        },
        {
          label: "System Settings",
          href: "/admin?tab=settings",
          icon: Shield,
          active: location.pathname === "/admin" && new URLSearchParams(location.search).get("tab") === "settings",
        },
      ];
    } else {
      return [
        {
          label: "Projects",
          href: "/projects",
          icon: Home,
          active: location.pathname === "/projects" || location.pathname === "/",
        },
        {
          label: "Settings",
          href: "/settings",
          icon: Settings,
          active: location.pathname === "/settings",
        },
      ];
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <div className="w-64 bg-card border-r border-border h-screen flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-border">
        <div className="flex items-center gap-3">
          <img src={connectaIcon} alt="ConnectaBuild" className="w-8 h-8" />
          <h1 className="text-xl font-bold">
            <span className="text-connecta-navy">Connecta</span>
            <span className="text-connecta-cyan">Build</span>
          </h1>
        </div>
      </div>

      {/* User Greeting */}
      <div className="px-6 py-4">
        <p className="text-foreground font-medium">
          {user ? `Hi ${user.first_name},` : "Welcome"}
        </p>
        {user && (
          <p className="text-sm text-muted-foreground capitalize">
            {user.user_type.replace('_', ' ')}
          </p>
        )}
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.href}>
                <Link
                  to={item.href}
                  className={cn(
                    "nav-item",
                    item.active && "active"
                  )}
                >
                  <Icon className="w-5 h-5" />
                  <span>{item.label}</span>
                </Link>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <button 
          onClick={handleResetDatabase}
          className="nav-item w-full justify-start text-muted-foreground hover:text-warning mb-2"
        >
          <RotateCcw className="w-5 h-5" />
          <span>Reset Database</span>
        </button>
        
        <button
          onClick={logout}
          className="nav-item w-full justify-start text-muted-foreground hover:text-destructive"
        >
          <LogOut className="w-5 h-5" />
          <span>Logout</span>
        </button>
        
        <div className="mt-4 text-xs text-muted-foreground">
          <p className="font-medium">Need Help? Contact us:</p>
          <p className="flex items-center gap-1 mt-1">
            📞 Mobile: 0409536295
          </p>
          <p className="flex items-center gap-1">
            ✉️ Email: <EMAIL>
          </p>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;