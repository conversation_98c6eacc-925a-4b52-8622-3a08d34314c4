import DashboardLayout from "@/components/DashboardLayout";
import ProjectsDashboard from "@/components/ProjectsDashboard";
import ServiceProviderDashboard from "@/components/ServiceProviderDashboard";
import InspectorDashboard from "@/components/InspectorDashboard";
import { usePermissions } from "@/hooks/usePermissions";

const Projects = () => {
  const { isServiceProvider, isInspector, isProjectAdmin } = usePermissions();

  const renderDashboard = () => {
    if (isServiceProvider()) {
      return (
        <div className="p-8">
          <ServiceProviderDashboard />
        </div>
      );
    }

    if (isInspector()) {
      return (
        <div className="p-8">
          <InspectorDashboard />
        </div>
      );
    }

    // Default for Project Admins and others
    return <ProjectsDashboard />;
  };

  return (
    <DashboardLayout>
      {renderDashboard()}
    </DashboardLayout>
  );
};

export default Projects;