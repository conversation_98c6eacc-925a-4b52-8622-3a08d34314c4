<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Project;
use App\Models\ProjectLevel;

class ProjectLevelController extends Controller
{
    /**
     * Display project levels.
     */
    public function index(Project $project): JsonResponse
    {
        $user = Auth::user();

        // Check if user has access to this project
        if (!$this->userCanAccessProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $levels = $project->levels()->orderBy('level_number')->get();

        return response()->json($levels);
    }

    /**
     * Store a newly created level.
     */
    public function store(Request $request, Project $project): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'level_name' => 'required|string|max:255',
            'level_number' => 'required|integer',
            'level_type' => 'required|in:basement,ground,floor,roof,mezzanine',
            'height' => 'nullable|numeric',
            'area' => 'nullable|numeric',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if level number already exists for this project
        if ($project->levels()->where('level_number', $request->level_number)->exists()) {
            return response()->json([
                'message' => 'Level number already exists for this project'
            ], 422);
        }

        $level = $project->levels()->create($request->validated());

        return response()->json($level, 201);
    }

    /**
     * Display the specified level.
     */
    public function show(Project $project, ProjectLevel $level): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanAccessProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Ensure level belongs to project
        if ($level->project_id !== $project->id) {
            return response()->json(['message' => 'Level not found'], 404);
        }

        return response()->json($level->load('workAreas'));
    }

    /**
     * Update the specified level.
     */
    public function update(Request $request, Project $project, ProjectLevel $level): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Ensure level belongs to project
        if ($level->project_id !== $project->id) {
            return response()->json(['message' => 'Level not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'level_name' => 'sometimes|string|max:255',
            'level_number' => 'sometimes|integer',
            'level_type' => 'sometimes|in:basement,ground,floor,roof,mezzanine',
            'height' => 'sometimes|nullable|numeric',
            'area' => 'sometimes|nullable|numeric',
            'description' => 'sometimes|nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if level number already exists for this project (excluding current level)
        if ($request->has('level_number')) {
            $existingLevel = $project->levels()
                ->where('level_number', $request->level_number)
                ->where('id', '!=', $level->id)
                ->exists();

            if ($existingLevel) {
                return response()->json([
                    'message' => 'Level number already exists for this project'
                ], 422);
            }
        }

        $level->update($request->validated());

        return response()->json($level);
    }

    /**
     * Remove the specified level.
     */
    public function destroy(Project $project, ProjectLevel $level): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Ensure level belongs to project
        if ($level->project_id !== $project->id) {
            return response()->json(['message' => 'Level not found'], 404);
        }

        // Check if level has work areas
        if ($level->workAreas()->exists()) {
            return response()->json([
                'message' => 'Cannot delete level with existing work areas'
            ], 422);
        }

        $level->delete();

        return response()->json(['message' => 'Level deleted successfully']);
    }

    /**
     * Check if user can access project.
     */
    private function userCanAccessProject($user, Project $project): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        return $project->hasMember($user);
    }

    /**
     * Check if user can update project.
     */
    private function userCanUpdateProject($user, Project $project): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        if ($project->project_admin_id === $user->id) {
            return true;
        }

        // Check if user is a project admin member
        $memberRole = $project->getMemberRole($user);
        return $memberRole === 'project_admin';
    }
}
