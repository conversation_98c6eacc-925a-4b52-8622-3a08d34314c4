import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  <PERSON>older<PERSON><PERSON>, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Upload,
  Camera,
  FileText,
  MapPin
} from "lucide-react";
import { usePermissions } from "@/hooks/usePermissions";

const ServiceProviderDashboard = () => {
  const { user } = usePermissions();
  const [stats, setStats] = useState({
    assignedProjects: 3,
    totalPenetrations: 45,
    completedPenetrations: 32,
    pendingPenetrations: 8,
    awaitingInspection: 5,
    completionRate: 71
  });

  const disciplineColors: { [key: string]: string } = {
    electrical: "bg-yellow-100 text-yellow-800",
    plumbing: "bg-blue-100 text-blue-800",
    dry_fire_services: "bg-orange-100 text-orange-800",
    hvac: "bg-purple-100 text-purple-800",
    wet_fire_services: "bg-red-100 text-red-800"
  };

  const recentPenetrations = [
    {
      id: "FP-001",
      project: "Tower Complex A",
      workArea: "Level 5 - Apartment 501",
      status: "completed",
      lastUpdated: "2 hours ago",
      type: "electrical"
    },
    {
      id: "FP-002", 
      project: "Office Building B",
      workArea: "Level 3 - Meeting Room",
      status: "pending",
      lastUpdated: "1 day ago",
      type: "electrical"
    },
    {
      id: "FP-003",
      project: "Residential Complex",
      workArea: "Level 2 - Kitchen",
      status: "awaiting_inspection",
      lastUpdated: "3 days ago",
      type: "electrical"
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'awaiting_inspection': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'pending': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'awaiting_inspection': return <AlertTriangle className="w-4 h-4 text-blue-600" />;
      default: return <Clock className="w-4 h-4 text-gray-600" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Welcome back, {user?.first_name}!
          </h1>
          <p className="text-muted-foreground">
            {user?.discipline && (
              <Badge className={disciplineColors[user.discipline] || "bg-gray-100 text-gray-800"}>
                {user.discipline.replace('_', ' ').toUpperCase()} Specialist
              </Badge>
            )}
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Camera className="w-4 h-4 mr-2" />
            Quick Photo
          </Button>
          <Button>
            <Upload className="w-4 h-4 mr-2" />
            Add Penetration
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned Projects</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.assignedProjects}</div>
            <p className="text-xs text-muted-foreground">
              Active projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Penetrations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPenetrations}</div>
            <p className="text-xs text-muted-foreground">
              {stats.completedPenetrations} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Work</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingPenetrations}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Awaiting Inspection</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.awaitingInspection}</div>
            <p className="text-xs text-muted-foreground">
              Ready for review
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Completion Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Work Progress</CardTitle>
          <CardDescription>Your completion rate across all assigned projects</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium">Overall Completion</span>
                <span className="text-sm font-bold text-green-600">{stats.completionRate}%</span>
              </div>
              <Progress value={stats.completionRate} className="h-2" />
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">{stats.completedPenetrations}</div>
              <div className="text-sm text-muted-foreground">of {stats.totalPenetrations}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Work */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Fire Penetrations</CardTitle>
          <CardDescription>Your latest work and updates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentPenetrations.map((penetration) => (
              <div key={penetration.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  {getStatusIcon(penetration.status)}
                  <div>
                    <p className="font-medium">{penetration.id}</p>
                    <p className="text-sm text-muted-foreground">{penetration.project}</p>
                    <p className="text-xs text-muted-foreground">{penetration.workArea}</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge className={getStatusColor(penetration.status)}>
                    {penetration.status.replace('_', ' ')}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">
                    {penetration.lastUpdated}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Add New Penetration</CardTitle>
            <CardDescription>Record a new fire penetration</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              <MapPin className="w-4 h-4 mr-2" />
              Add Penetration
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Upload Photos</CardTitle>
            <CardDescription>Add site or system images</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              <Camera className="w-4 h-4 mr-2" />
              Upload Images
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Generate Report</CardTitle>
            <CardDescription>Create work summary report</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              <FileText className="w-4 h-4 mr-2" />
              Create Report
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ServiceProviderDashboard;
