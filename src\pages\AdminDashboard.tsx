import DashboardLayout from "@/components/DashboardLayout";
import ManageProjects from "@/components/ManageProjects";
import ManageUsers from "@/components/ManageUsers";
import { useSearchParams } from "react-router-dom";

const AdminDashboard = () => {
  const [searchParams] = useSearchParams();
  const activeTab = searchParams.get("tab") || "projects";

  return (
    <DashboardLayout>
      <div className="p-8">
        {activeTab === "projects" && <ManageProjects />}
        {activeTab === "users" && <ManageUsers />}
      </div>
    </DashboardLayout>
  );
};

export default AdminDashboard;