<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\FirePenetrationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::group([
    'middleware' => 'api',
    'prefix' => 'auth'
], function ($router) {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('refresh', [AuthController::class, 'refresh']);
    Route::get('user-profile', [AuthController::class, 'userProfile']);
});

// Protected routes
Route::middleware('auth:api')->group(function () {
    
    // User management routes
    Route::apiResource('users', UserController::class);
    
    // Project management routes
    Route::apiResource('projects', ProjectController::class);
    Route::post('projects/{project}/members', [ProjectController::class, 'addMember']);
    Route::delete('projects/{project}/members/{user}', [ProjectController::class, 'removeMember']);
    Route::put('projects/{project}/members/{user}', [ProjectController::class, 'updateMember']);
    
    // Fire penetration routes
    Route::apiResource('fire-penetrations', FirePenetrationController::class);
    Route::post('fire-penetrations/{penetration}/images', [FirePenetrationController::class, 'uploadImage']);
    Route::post('fire-penetrations/{penetration}/comments', [FirePenetrationController::class, 'addComment']);
    
    // Work area routes
    Route::get('projects/{project}/work-areas', [ProjectController::class, 'getWorkAreas']);
    Route::post('projects/{project}/work-areas', [ProjectController::class, 'createWorkArea']);
    Route::put('work-areas/{workArea}', [ProjectController::class, 'updateWorkArea']);
    Route::delete('work-areas/{workArea}', [ProjectController::class, 'deleteWorkArea']);
    
    // Project level routes
    Route::get('projects/{project}/levels', [ProjectController::class, 'getLevels']);
    Route::post('projects/{project}/levels', [ProjectController::class, 'createLevel']);
    Route::put('levels/{level}', [ProjectController::class, 'updateLevel']);
    Route::delete('levels/{level}', [ProjectController::class, 'deleteLevel']);
    
    // Plan overlay routes
    Route::get('projects/{project}/overlays', [ProjectController::class, 'getOverlays']);
    Route::post('projects/{project}/overlays', [ProjectController::class, 'uploadOverlay']);
    Route::delete('overlays/{overlay}', [ProjectController::class, 'deleteOverlay']);
    
    // Manufacturer and fire stopping system routes
    Route::get('manufacturers', [UserController::class, 'getManufacturers']);
    Route::post('manufacturers', [UserController::class, 'createManufacturer']);
    Route::get('fire-stopping-systems', [UserController::class, 'getFireStoppingSystems']);
    Route::post('fire-stopping-systems', [UserController::class, 'createFireStoppingSystem']);
    
    // Dashboard and analytics routes
    Route::get('dashboard/stats', [UserController::class, 'getDashboardStats']);
    Route::get('dashboard/recent-activity', [UserController::class, 'getRecentActivity']);
    
    // Super admin routes
    Route::middleware('role:super_admin')->group(function () {
        Route::get('admin/users', [UserController::class, 'getAllUsers']);
        Route::post('admin/users', [UserController::class, 'createUser']);
        Route::put('admin/users/{user}', [UserController::class, 'updateUser']);
        Route::delete('admin/users/{user}', [UserController::class, 'deleteUser']);
        Route::get('admin/projects', [ProjectController::class, 'getAllProjects']);
        Route::get('admin/analytics', [UserController::class, 'getSystemAnalytics']);
    });
});
