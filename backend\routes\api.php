<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ProjectController;
use App\Http\Controllers\FirePenetrationController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Public routes
Route::group([
    'middleware' => 'api',
    'prefix' => 'auth'
], function ($router) {
    Route::post('login', [AuthController::class, 'login']);
    Route::post('register', [AuthController::class, 'register']);
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('refresh', [AuthController::class, 'refresh']);
    Route::get('user-profile', [AuthController::class, 'userProfile']);
});

// Protected routes
Route::middleware('auth:api')->group(function () {

    // User profile routes
    Route::put('auth/profile', [UserController::class, 'updateProfile']);
    Route::put('auth/change-password', [UserController::class, 'changePassword']);
    Route::put('auth/notification-settings', [UserController::class, 'updateNotificationSettings']);
    Route::put('auth/security-settings', [UserController::class, 'updateSecuritySettings']);
    Route::post('auth/avatar', [UserController::class, 'uploadAvatar']);
    Route::delete('auth/account', [UserController::class, 'deleteAccount']);

    // User management routes
    Route::apiResource('users', UserController::class);
    
    // Project management routes
    Route::apiResource('projects', ProjectController::class);
    Route::post('projects/{project}/members', [ProjectController::class, 'addMember']);
    Route::delete('projects/{project}/members/{user}', [ProjectController::class, 'removeMember']);
    Route::put('projects/{project}/members/{user}', [ProjectController::class, 'updateMember']);
    
    // Fire penetration routes
    Route::get('projects/{project}/fire-penetrations', [FirePenetrationController::class, 'index']);
    Route::post('projects/{project}/fire-penetrations', [FirePenetrationController::class, 'store']);
    Route::get('fire-penetrations/{firePenetration}', [FirePenetrationController::class, 'show']);
    Route::put('fire-penetrations/{firePenetration}', [FirePenetrationController::class, 'update']);
    Route::delete('fire-penetrations/{firePenetration}', [FirePenetrationController::class, 'destroy']);
    Route::patch('fire-penetrations/{firePenetration}/status', [FirePenetrationController::class, 'updateStatus']);
    Route::post('fire-penetrations/{firePenetration}/request-inspection', [FirePenetrationController::class, 'requestInspection']);
    Route::post('fire-penetrations/{firePenetration}/inspect', [FirePenetrationController::class, 'performInspection']);
    Route::post('fire-penetrations/{firePenetration}/images', [FirePenetrationController::class, 'uploadImages']);
    Route::get('fire-penetrations/{firePenetration}/images', [FirePenetrationController::class, 'getImages']);

    // Work area routes
    Route::get('projects/{project}/work-areas', [WorkAreaController::class, 'index']);
    Route::post('projects/{project}/work-areas', [WorkAreaController::class, 'store']);
    Route::get('projects/{project}/work-areas/{workArea}', [WorkAreaController::class, 'show']);
    Route::put('projects/{project}/work-areas/{workArea}', [WorkAreaController::class, 'update']);
    Route::delete('projects/{project}/work-areas/{workArea}', [WorkAreaController::class, 'destroy']);

    // Project level routes
    Route::get('projects/{project}/levels', [ProjectLevelController::class, 'index']);
    Route::post('projects/{project}/levels', [ProjectLevelController::class, 'store']);
    Route::get('projects/{project}/levels/{level}', [ProjectLevelController::class, 'show']);
    Route::put('projects/{project}/levels/{level}', [ProjectLevelController::class, 'update']);
    Route::delete('projects/{project}/levels/{level}', [ProjectLevelController::class, 'destroy']);
    
    // Plan overlay routes
    Route::get('projects/{project}/overlays', [ProjectController::class, 'getOverlays']);
    Route::post('projects/{project}/overlays', [ProjectController::class, 'uploadOverlay']);
    Route::delete('overlays/{overlay}', [ProjectController::class, 'deleteOverlay']);
    
    // Manufacturer and fire stopping system routes
    Route::get('manufacturers', [UserController::class, 'getManufacturers']);
    Route::post('manufacturers', [UserController::class, 'createManufacturer']);
    Route::get('fire-stopping-systems', [UserController::class, 'getFireStoppingSystems']);
    Route::post('fire-stopping-systems', [UserController::class, 'createFireStoppingSystem']);
    
    // Dashboard and analytics routes
    Route::get('dashboard/stats', [UserController::class, 'getDashboardStats']);
    Route::get('dashboard/recent-activity', [UserController::class, 'getRecentActivity']);
    
    // Super admin routes
    Route::middleware('role:super_admin')->group(function () {
        Route::get('admin/users', [UserController::class, 'getAllUsers']);
        Route::post('admin/users', [UserController::class, 'createUser']);
        Route::put('admin/users/{user}', [UserController::class, 'updateUser']);
        Route::delete('admin/users/{user}', [UserController::class, 'deleteUser']);
        Route::get('admin/projects', [ProjectController::class, 'getAllProjects']);
        Route::get('admin/analytics', [UserController::class, 'getSystemAnalytics']);
    });
});
