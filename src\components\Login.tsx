import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth } from "@/contexts/AuthContext";
import connectaIcon from "@/assets/connecta-logo-icon.png";
import connectaIllustration from "@/assets/connecta-illustration.png";

const Login = () => {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("password123");
  const [keepLoggedIn, setKeepLoggedIn] = useState(true);
  const [error, setError] = useState("");
  const { login, loading } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    try {
      await login(email, password);
    } catch (err: any) {
      setError(err.message || "<PERSON><PERSON> failed. Please try again.");
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left side - Illustration */}
      <div className="hidden lg:flex lg:w-1/2 bg-background items-center justify-center p-12">
        <div className="max-w-lg">
          {/* Logo */}
          <div className="flex items-center mb-12">
            <img 
              src={connectaIcon} 
              alt="ConnectaBuild" 
              className="w-12 h-12 mr-4"
            />
            <h1 className="text-4xl font-bold">
              <span className="text-connecta-navy">Connecta</span>
              <span className="text-connecta-cyan">Build</span>
            </h1>
          </div>
          
          {/* Illustration */}
          <div className="flex justify-center">
            <img 
              src={connectaIllustration} 
              alt="ConnectaBuild Platform" 
              className="w-full max-w-md"
            />
          </div>
        </div>
      </div>

      {/* Right side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md space-y-8">
          {/* Mobile Logo */}
          <div className="lg:hidden flex items-center justify-center mb-8">
            <img 
              src={connectaIcon} 
              alt="ConnectaBuild" 
              className="w-10 h-10 mr-3"
            />
            <h1 className="text-3xl font-bold">
              <span className="text-connecta-navy">Connecta</span>
              <span className="text-connecta-cyan">Build</span>
            </h1>
          </div>

          {/* Welcome Header */}
          <div className="text-center lg:text-left">
            <h2 className="text-3xl font-bold text-connecta-navy mb-2">Welcome Back</h2>
            <p className="text-muted-foreground">
              Are you new to ConnectaBuild?{" "}
              <a href="#" className="text-connecta-cyan hover:underline font-medium">
                Create An Account
              </a>
            </p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email" className="text-connecta-navy font-medium">
                Email Address
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="h-12 border-border focus:border-connecta-cyan focus:ring-connecta-cyan"
                placeholder="Enter your email"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-connecta-navy font-medium">
                Password
              </Label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="h-12 border-border focus:border-connecta-cyan focus:ring-connecta-cyan"
                placeholder="Enter your password"
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="keep-logged-in"
                  checked={keepLoggedIn}
                  onCheckedChange={(checked) => setKeepLoggedIn(checked === true)}
                  className="border-border data-[state=checked]:bg-connecta-cyan data-[state=checked]:border-connecta-cyan"
                />
                <Label 
                  htmlFor="keep-logged-in" 
                  className="text-sm text-foreground font-normal cursor-pointer"
                >
                  Keep me logged in
                </Label>
              </div>
              
              <a 
                href="#" 
                className="text-sm text-connecta-cyan hover:underline font-medium"
              >
                Reset Password
              </a>
            </div>

            <Button
              type="submit"
              disabled={loading}
              className="w-full h-12 bg-connecta-dark-navy hover:bg-connecta-navy text-white font-medium text-lg disabled:opacity-50"
            >
              {loading ? "Logging in..." : "Login"}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Login;