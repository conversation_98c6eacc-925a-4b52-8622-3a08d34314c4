<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Manufacturer;
use App\Models\FireStoppingSystem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create Super Admin
        User::create([
            'first_name' => 'Super',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'ConnectaBuild',
            'phone' => '0409536295',
            'user_type' => 'super_admin',
            'discipline' => null,
            'status' => 'active',
        ]);

        // Create Project Admin
        User::create([
            'first_name' => 'Jacob',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'ABC Construction',
            'phone' => '**********',
            'user_type' => 'project_admin',
            'discipline' => null,
            'status' => 'active',
        ]);

        // Create Service Providers
        User::create([
            'first_name' => 'John',
            'last_name' => 'Electrician',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'Spark Electrical',
            'phone' => '**********',
            'user_type' => 'service_provider',
            'discipline' => 'electrical',
            'status' => 'active',
        ]);

        User::create([
            'first_name' => 'Mike',
            'last_name' => 'Plumber',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'Flow Plumbing',
            'phone' => '**********',
            'user_type' => 'service_provider',
            'discipline' => 'plumbing',
            'status' => 'active',
        ]);

        // Create Inspector
        User::create([
            'first_name' => 'Sarah',
            'last_name' => 'Inspector',
            'email' => '<EMAIL>',
            'password' => Hash::make('password123'),
            'company_name' => 'Quality Inspections',
            'phone' => '**********',
            'user_type' => 'inspector',
            'discipline' => null,
            'status' => 'active',
        ]);

        // Create Manufacturers
        $manufacturer1 = Manufacturer::create([
            'name' => 'Hilti Australia',
            'contact_person' => 'David Wilson',
            'phone' => '1800-HILTI',
            'email' => '<EMAIL>',
        ]);

        $manufacturer2 = Manufacturer::create([
            'name' => 'Nullifire',
            'contact_person' => 'Emma Thompson',
            'phone' => '1300-NULL-FIRE',
            'email' => '<EMAIL>',
        ]);

        // Create Fire Stopping Systems
        FireStoppingSystem::create([
            'matrix_id' => 'FS-101',
            'manufacturer_id' => $manufacturer1->id,
            'system_name' => 'Hilti Firestop Foam CFS-F',
            'service_types' => ['electrical', 'plumbing'],
            'frl_rating' => '120/120/120',
        ]);

        FireStoppingSystem::create([
            'matrix_id' => 'FS-102',
            'manufacturer_id' => $manufacturer2->id,
            'system_name' => 'Nullifire FS702 Intumescent Sealant',
            'service_types' => ['electrical', 'hvac'],
            'frl_rating' => '90/90/90',
        ]);
    }
}
