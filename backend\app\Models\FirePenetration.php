<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FirePenetration extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'work_area_id',
        'service_provider_id',
        'inspector_id',
        'fire_stopping_system_id',
        'service_id',
        'service_name',
        'service_type',
        'service_description',
        'tag',
        'team_contact',
        'substrate_type',
        'substrate_material',
        'frl_rating',
        'cast_in_slab',
        'performance_solution',
        'inspection_requested',
        'coordinates_x',
        'coordinates_y',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'cast_in_slab' => 'boolean',
            'performance_solution' => 'boolean',
            'inspection_requested' => 'boolean',
            'coordinates_x' => 'decimal:6',
            'coordinates_y' => 'decimal:6',
        ];
    }

    /**
     * Get the project that owns the penetration.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the work area that owns the penetration.
     */
    public function workArea()
    {
        return $this->belongsTo(WorkArea::class);
    }

    /**
     * Get the service provider assigned to this penetration.
     */
    public function serviceProvider()
    {
        return $this->belongsTo(User::class, 'service_provider_id');
    }

    /**
     * Get the inspector assigned to this penetration.
     */
    public function inspector()
    {
        return $this->belongsTo(User::class, 'inspector_id');
    }

    /**
     * Get the fire stopping system for this penetration.
     */
    public function fireStoppingSystem()
    {
        return $this->belongsTo(FireStoppingSystem::class);
    }

    /**
     * Get images for this penetration.
     */
    public function images()
    {
        return $this->hasMany(PenetrationImage::class, 'penetration_id');
    }

    /**
     * Get comments for this penetration.
     */
    public function comments()
    {
        return $this->hasMany(PenetrationComment::class, 'penetration_id');
    }

    /**
     * Get coordinates as array.
     */
    public function getCoordinatesAttribute(): array
    {
        return [
            'x' => $this->coordinates_x,
            'y' => $this->coordinates_y,
        ];
    }
}
