<?php

namespace Faker\Provider\es_AR;

class PhoneNumber extends \Faker\Provider\PhoneNumber
{
    protected static $formats = [
        '+##(#)##########',
        '+##(#)##########',
        '###-###-####',
        '(##)4###-####',
        '(##)4###-####',
        '(##)4###-####',
        '(##)4###-####',
        '(##)154###-####',
        '(##)5###-####',
        '(##)5###-####',
        '(##)155###-####',
        '(##)155###-####',
        '(##)155###-####',
        '(##)6###-####',
        '(###)4##-####',
        '(###)4##-####',
        '(###)4##-####',
        '(###)5##-####',
        '(###)5##-####',
        '(###)5##-####',
        '(###)154##-####',
        '(###)154##-####',
        '(###)155##-####',
        '(###)155##-####',
        '(###)155##-####',
        '(####)4#-####',
        '(####)4#-####',
        '(####)4#-####',
        '(####)4#-####',
        '(####)154#-####',
        '(####)154#-####',
        '(####)154#-####',
        '(####)155#-####',
    ];
}
