<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PenetrationImage extends Model
{
    use HasFactory;

    protected $fillable = [
        'penetration_id',
        'uploaded_by',
        'file_path',
        'image_type',
    ];

    /**
     * Get the penetration that owns the image.
     */
    public function penetration()
    {
        return $this->belongsTo(FirePenetration::class, 'penetration_id');
    }

    /**
     * Get the user who uploaded the image.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
}
