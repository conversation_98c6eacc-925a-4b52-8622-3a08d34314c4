import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { User } from '@/types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  requireAuth?: boolean;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles = [],
  requireAuth = true,
  redirectTo = '/login'
}) => {
  const { user, isAuthenticated, loading } = useAuth();
  const location = useLocation();

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-connecta-cyan"></div>
      </div>
    );
  }

  // Check if authentication is required
  if (requireAuth && !isAuthenticated) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  // Check if user has required role
  if (allowedRoles.length > 0 && user) {
    const hasRequiredRole = allowedRoles.includes(user.user_type);
    
    if (!hasRequiredRole) {
      // Redirect based on user's actual role
      const redirectPath = getDefaultRouteForRole(user.user_type);
      return <Navigate to={redirectPath} replace />;
    }
  }

  return <>{children}</>;
};

// Helper function to get default route based on user role
const getDefaultRouteForRole = (userType: string): string => {
  switch (userType) {
    case 'super_admin':
      return '/admin';
    case 'project_admin':
      return '/projects';
    case 'service_provider':
      return '/projects';
    case 'inspector':
      return '/projects';
    case 'guest':
      return '/';
    default:
      return '/';
  }
};

export default ProtectedRoute;
