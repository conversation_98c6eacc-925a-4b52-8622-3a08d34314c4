import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { authApi } from '@/api/connectaBuildApi';
import { User } from '@/types';

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing token on app load
    const storedToken = localStorage.getItem('auth_token');
    const storedUser = localStorage.getItem('user');
    
    if (storedToken && storedUser) {
      setToken(storedToken);
      setUser(JSON.parse(storedUser));
    }
    
    setLoading(false);
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setLoading(true);
      const response = await authApi.login(email, password);
      
      const { access_token, user: userData } = response;
      
      // Store token and user data
      localStorage.setItem('auth_token', access_token);
      localStorage.setItem('user', JSON.stringify(userData));
      localStorage.setItem('userRole', getUserRoleDisplay(userData.user_type));
      localStorage.setItem('userEmail', userData.email);
      
      setToken(access_token);
      setUser(userData);
      
      // Redirect based on user role
      if (userData.user_type === 'super_admin') {
        window.location.href = '/admin';
      } else if (userData.user_type === 'project_admin' || userData.user_type === 'service_provider') {
        window.location.href = '/projects';
      } else {
        window.location.href = '/';
      }
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.response?.data?.error || 'Login failed');
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear local storage regardless of API call success
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      localStorage.removeItem('userRole');
      localStorage.removeItem('userEmail');
      
      setToken(null);
      setUser(null);
      
      window.location.href = '/login';
    }
  };

  const getUserRoleDisplay = (userType: string): string => {
    switch (userType) {
      case 'super_admin':
        return 'Super Admin';
      case 'project_admin':
        return 'Project Admin';
      case 'service_provider':
        return 'Service Provider';
      case 'inspector':
        return 'Inspector';
      case 'guest':
        return 'Guest';
      default:
        return 'Guest';
    }
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    logout,
    loading,
    isAuthenticated: !!token && !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
