import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { 
  Users, 
  FolderOpen, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Building,
  Shield,
  Activity,
  BarChart3
} from "lucide-react";
import { useAppDispatch } from "@/hooks/useAppDispatch";
import { useAppSelector } from "@/hooks/useAppSelector";
import { fetchUsers } from "@/store/slices/usersSlice";
import { fetchProjects } from "@/store/slices/projectsSlice";

const SuperAdminDashboard = () => {
  const dispatch = useAppDispatch();
  const { users } = useAppSelector((state) => state.users);
  const { projects } = useAppSelector((state) => state.projects);
  const [systemStats, setSystemStats] = useState({
    totalUsers: 0,
    activeUsers: 0,
    totalProjects: 0,
    activeProjects: 0,
    totalPenetrations: 0,
    completedPenetrations: 0,
    pendingInspections: 0,
    systemHealth: 98
  });

  useEffect(() => {
    dispatch(fetchUsers());
    dispatch(fetchProjects());
  }, [dispatch]);

  useEffect(() => {
    // Calculate system statistics
    const activeUsers = users.filter(user => user.status === 'active').length;
    const activeProjects = projects.filter(project => project.status === 'published').length;
    
    setSystemStats({
      totalUsers: users.length,
      activeUsers,
      totalProjects: projects.length,
      activeProjects,
      totalPenetrations: 1247, // Mock data - would come from API
      completedPenetrations: 892,
      pendingInspections: 23,
      systemHealth: 98
    });
  }, [users, projects]);

  const getHealthColor = (health: number) => {
    if (health >= 95) return "text-green-600";
    if (health >= 85) return "text-yellow-600";
    return "text-red-600";
  };

  const recentActivity = [
    { id: 1, action: "New user registered", user: "John Smith", time: "2 minutes ago", type: "user" },
    { id: 2, action: "Project completed", project: "Tower Complex A", time: "15 minutes ago", type: "project" },
    { id: 3, action: "Inspection requested", project: "Office Building B", time: "1 hour ago", type: "inspection" },
    { id: 4, action: "Fire penetration added", project: "Residential Complex", time: "2 hours ago", type: "penetration" },
    { id: 5, action: "User role updated", user: "Sarah Wilson", time: "3 hours ago", type: "user" }
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user': return <Users className="w-4 h-4 text-blue-500" />;
      case 'project': return <FolderOpen className="w-4 h-4 text-green-500" />;
      case 'inspection': return <Shield className="w-4 h-4 text-orange-500" />;
      case 'penetration': return <Activity className="w-4 h-4 text-purple-500" />;
      default: return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Super Admin Dashboard</h1>
          <p className="text-muted-foreground">System overview and management</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <BarChart3 className="w-4 h-4 mr-2" />
            Generate Report
          </Button>
          <Button>
            <TrendingUp className="w-4 h-4 mr-2" />
            View Analytics
          </Button>
        </div>
      </div>

      {/* System Health */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2" />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="flex justify-between mb-2">
                <span className="text-sm font-medium">Overall Health</span>
                <span className={`text-sm font-bold ${getHealthColor(systemStats.systemHealth)}`}>
                  {systemStats.systemHealth}%
                </span>
              </div>
              <Progress value={systemStats.systemHealth} className="h-2" />
            </div>
            <Badge variant={systemStats.systemHealth >= 95 ? "default" : "destructive"}>
              {systemStats.systemHealth >= 95 ? "Excellent" : "Needs Attention"}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {systemStats.activeUsers} active users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.totalProjects}</div>
            <p className="text-xs text-muted-foreground">
              {systemStats.activeProjects} active projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fire Penetrations</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.totalPenetrations}</div>
            <p className="text-xs text-muted-foreground">
              {systemStats.completedPenetrations} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Inspections</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{systemStats.pendingInspections}</div>
            <p className="text-xs text-muted-foreground">
              Requires attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>Latest system activities and updates</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="flex items-center space-x-4">
                {getActivityIcon(activity.type)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground">
                    {activity.action}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {activity.user || activity.project}
                  </p>
                </div>
                <div className="text-sm text-muted-foreground">
                  {activity.time}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">User Management</CardTitle>
            <CardDescription>Manage system users and permissions</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              Manage Users
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">System Settings</CardTitle>
            <CardDescription>Configure global system settings</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              Open Settings
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Analytics</CardTitle>
            <CardDescription>View detailed system analytics</CardDescription>
          </CardHeader>
          <CardContent>
            <Button className="w-full" variant="outline">
              View Reports
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SuperAdminDashboard;
