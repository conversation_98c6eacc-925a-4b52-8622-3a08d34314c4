import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { WorkArea, WorkAreaShape } from '@/types';
import { projectsApi } from '@/api/connectaBuildApi';

interface WorkAreasState {
  workAreas: WorkArea[];
  currentWorkArea: WorkArea | null;
  loading: boolean;
  error: string | null;
  autoSaveEnabled: boolean;
  lastAutoSave?: string;
}

const initialState: WorkAreasState = {
  workAreas: [],
  currentWorkArea: null,
  loading: false,
  error: null,
  autoSaveEnabled: true,
};

// Async thunks
export const fetchWorkAreasByProjectId = createAsyncThunk(
  'workAreas/fetchByProjectId',
  async (projectId: number) => {
    const response = await projectsApi.getWorkAreas(projectId);
    return response.data || response;
  }
);

export const fetchWorkAreasByLevelId = createAsyncThunk(
  'workAreas/fetchByLevelId',
  async ({ projectId, levelId }: { projectId: number; levelId: number }) => {
    const response = await projectsApi.getWorkAreas(projectId);
    const workAreas = response.data || response;
    return workAreas.filter((area: any) => area.level_id === levelId);
  }
);

export const createWorkArea = createAsyncThunk(
  'workAreas/createWorkArea',
  async ({ projectId, workAreaData }: { projectId: number; workAreaData: any }) => {
    const response = await projectsApi.createWorkArea(projectId, workAreaData);
    return response.data || response;
  }
);

export const updateWorkArea = createAsyncThunk(
  'workAreas/updateWorkArea',
  async ({ projectId, id, workAreaData }: { projectId: number; id: number; workAreaData: any }) => {
    const response = await projectsApi.updateWorkArea(projectId, id, workAreaData);
    return response.data || response;
  }
);

export const deleteWorkArea = createAsyncThunk(
  'workAreas/deleteWorkArea',
  async ({ projectId, id }: { projectId: number; id: number | string }) => {
    if (typeof id === 'string') {
      // Handle pending area deletion from localStorage
      const areas = JSON.parse(localStorage.getItem('workAreaDrafts') || '[]');
      const filteredAreas = areas.filter((d: any) => d.id !== id);
      localStorage.setItem('workAreaDrafts', JSON.stringify(filteredAreas));
      return id;
    } else {
      await projectsApi.deleteWorkArea(projectId, id);
      return id;
    }
  }
);

// New pending area actions
export const createDraft = createAsyncThunk(
  'workAreas/createPending',
  async (draftData: { projectId: number; levelId: number }) => {
    const newDraft: WorkArea = {
      id: `draft_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      project_id: draftData.projectId,
      level_id: draftData.levelId,
      area_code: '',
      area_name: '',
      area_type: 'floor',
      category: 'apartment',
      frl_rating: '',
      coordinates: [],
      color: '#3b82f6',
      shapes: [],
      tags: [],
      pending: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    // Save to localStorage
    const drafts = JSON.parse(localStorage.getItem('workAreaDrafts') || '[]');
    drafts.push(newDraft);
    localStorage.setItem('workAreaDrafts', JSON.stringify(drafts));

    return newDraft;
  }
);

export const saveDraft = createAsyncThunk(
  'workAreas/saveDraft',
  async (workArea: WorkArea) => {
    const drafts = JSON.parse(localStorage.getItem('workAreaDrafts') || '[]');
    const existingIndex = drafts.findIndex((d: any) => d.id === workArea.id);

    const updatedDraft = {
      ...workArea,
      updated_at: new Date().toISOString(),
      auto_saved_at: new Date().toISOString()
    };

    if (existingIndex >= 0) {
      drafts[existingIndex] = updatedDraft;
    } else {
      drafts.push(updatedDraft);
    }

    localStorage.setItem('workAreaDrafts', JSON.stringify(drafts));
    return updatedDraft;
  }
);

export const loadDrafts = createAsyncThunk(
  'workAreas/loadDrafts',
  async () => {
    const drafts = JSON.parse(localStorage.getItem('workAreaDrafts') || '[]');
    return drafts;
  }
);

const workAreasSlice = createSlice({
  name: 'workAreas',
  initialState,
  reducers: {
    setCurrentWorkArea: (state, action) => {
      state.currentWorkArea = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    // Local state management for drawing
    addWorkAreaPoint: (state, action) => {
      const { workAreaId, point } = action.payload;
      const workArea = state.workAreas.find(w => w.id === workAreaId);
      if (workArea) {
        if (!workArea.coordinates) {
          workArea.coordinates = [];
        }
        workArea.coordinates.push(point);
      }
    },
    updateWorkAreaPoints: (state, action) => {
      const { workAreaId, coordinates } = action.payload;
      const workArea = state.workAreas.find(w => w.id === workAreaId);
      if (workArea) {
        workArea.coordinates = coordinates;
      }
    },
    // Draft management reducers
    updateCurrentWorkArea: (state, action: PayloadAction<Partial<WorkArea>>) => {
      if (state.currentWorkArea) {
        state.currentWorkArea = { ...state.currentWorkArea, ...action.payload };
        // Update in workAreas array if it exists
        const index = state.workAreas.findIndex(w => w.id === state.currentWorkArea?.id);
        if (index !== -1) {
          state.workAreas[index] = state.currentWorkArea;
        }
      }
    },
    addShapeToCurrentWorkArea: (state, action: PayloadAction<WorkAreaShape>) => {
      if (state.currentWorkArea) {
        if (!state.currentWorkArea.shapes) {
          state.currentWorkArea.shapes = [];
        }
        state.currentWorkArea.shapes.push(action.payload);
        // Update in workAreas array if it exists
        const index = state.workAreas.findIndex(w => w.id === state.currentWorkArea?.id);
        if (index !== -1) {
          state.workAreas[index] = state.currentWorkArea;
        }
      }
    },
    removeShapeFromCurrentWorkArea: (state, action: PayloadAction<string>) => {
      if (state.currentWorkArea?.shapes) {
        state.currentWorkArea.shapes = state.currentWorkArea.shapes.filter(
          shape => shape.id !== action.payload
        );
        // Update in workAreas array if it exists
        const index = state.workAreas.findIndex(w => w.id === state.currentWorkArea?.id);
        if (index !== -1) {
          state.workAreas[index] = state.currentWorkArea;
        }
      }
    },
    setAutoSaveEnabled: (state, action: PayloadAction<boolean>) => {
      state.autoSaveEnabled = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch work areas by project ID
      .addCase(fetchWorkAreasByProjectId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchWorkAreasByProjectId.fulfilled, (state, action) => {
        state.loading = false;
        state.workAreas = action.payload;
      })
      .addCase(fetchWorkAreasByProjectId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch work areas';
      })
      // Fetch work areas by level ID
      .addCase(fetchWorkAreasByLevelId.fulfilled, (state, action) => {
        state.workAreas = action.payload;
      })
      // Create work area
      .addCase(createWorkArea.fulfilled, (state, action) => {
        const newWorkArea = action.payload;

        // If this is a published work area (pending: false), remove any pending versions
        // of the same overlay (same area_code or area_name)
        if (!newWorkArea.pending) {
          state.workAreas = state.workAreas.filter(wa => {
            // Keep work areas that don't match this overlay
            const isSameOverlay = (
              (wa.area_code && newWorkArea.area_code && wa.area_code === newWorkArea.area_code) ||
              (wa.area_name && newWorkArea.area_name && wa.area_name === newWorkArea.area_name)
            ) && wa.project_id === newWorkArea.project_id;

            // Remove pending versions of the same overlay
            return !(isSameOverlay && wa.pending);
          });

          // Also remove from localStorage drafts
          const drafts = JSON.parse(localStorage.getItem('workAreaDrafts') || '[]');
          const filteredDrafts = drafts.filter((draft: WorkArea) => {
            const isSameOverlay = (
              (draft.area_code && newWorkArea.area_code && draft.area_code === newWorkArea.area_code) ||
              (draft.area_name && newWorkArea.area_name && draft.area_name === newWorkArea.area_name)
            ) && draft.project_id === newWorkArea.project_id;

            return !isSameOverlay;
          });
          localStorage.setItem('workAreaDrafts', JSON.stringify(filteredDrafts));
        }

        state.workAreas.push(newWorkArea);
      })
      // Update work area
      .addCase(updateWorkArea.fulfilled, (state, action) => {
        const index = state.workAreas.findIndex(area => area.id === action.payload.id);
        if (index !== -1) {
          state.workAreas[index] = action.payload;
        }
      })
      // Delete work area
      .addCase(deleteWorkArea.fulfilled, (state, action) => {
        state.workAreas = state.workAreas.filter(area => area.id !== action.payload);
      })
      // Draft actions
      .addCase(createDraft.fulfilled, (state, action) => {
        state.workAreas.push(action.payload);
        state.currentWorkArea = action.payload;
      })
      .addCase(saveDraft.fulfilled, (state, action) => {
        const index = state.workAreas.findIndex(area => area.id === action.payload.id);
        if (index !== -1) {
          state.workAreas[index] = action.payload;
        } else {
          state.workAreas.push(action.payload);
        }
        if (state.currentWorkArea?.id === action.payload.id) {
          state.currentWorkArea = action.payload;
        }
        state.lastAutoSave = new Date().toISOString();
      })
      .addCase(loadDrafts.fulfilled, (state, action) => {
        // Merge pending areas with existing work areas, avoiding duplicates
        const pendingIds = new Set(state.workAreas.filter(w => w.pending).map(w => w.id));
        const newPending = action.payload.filter((area: WorkArea) => !pendingIds.has(area.id));
        state.workAreas.push(...newPending);
      });
  },
});

export const {
  setCurrentWorkArea,
  clearError,
  addWorkAreaPoint,
  updateWorkAreaPoints,
  updateCurrentWorkArea,
  addShapeToCurrentWorkArea,
  removeShapeFromCurrentWorkArea,
  setAutoSaveEnabled
} = workAreasSlice.actions;
export default workAreasSlice.reducer;