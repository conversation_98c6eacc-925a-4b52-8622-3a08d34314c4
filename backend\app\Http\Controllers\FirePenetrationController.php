<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use App\Models\Project;
use App\Models\FirePenetration;
use App\Models\PenetrationImage;

class FirePenetrationController extends Controller
{
    /**
     * Display fire penetrations for a project.
     */
    public function index(Request $request, Project $project): JsonResponse
    {
        $user = Auth::user();

        // Check if user has access to this project
        if (!$this->userCanAccessProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $page = $request->get('page', 1);
        $limit = $request->get('limit', 15);

        $query = $project->firePenetrations()
            ->with(['serviceProvider', 'inspector', 'workArea']);

        // Filter by user role
        if ($user->user_type === 'service_provider') {
            $query->where('service_provider_id', $user->id);
        }

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('service_type')) {
            $query->where('service_type', $request->service_type);
        }

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('service_id', 'like', "%{$search}%")
                  ->orWhere('service_name', 'like', "%{$search}%")
                  ->orWhere('tag', 'like', "%{$search}%");
            });
        }

        $penetrations = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'data' => $penetrations->items(),
            'current_page' => $penetrations->currentPage(),
            'last_page' => $penetrations->lastPage(),
            'per_page' => $penetrations->perPage(),
            'total' => $penetrations->total(),
        ]);
    }

    /**
     * Store a newly created fire penetration.
     */
    public function store(Request $request, Project $project): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanAccessProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'work_area_id' => 'required|exists:work_areas,id',
            'service_id' => 'required|string|max:50',
            'service_name' => 'required|string|max:255',
            'service_type' => 'required|in:electrical,plumbing,dry_fire_services,hvac,wet_fire_services',
            'service_description' => 'nullable|string',
            'tag' => 'required|string|max:50',
            'team_contact' => 'nullable|string|max:255',
            'substrate_type' => 'required|in:wall,floor,ceiling',
            'substrate_material' => 'required|in:concrete,hebel,steel,timber,plasterboard',
            'frl_rating' => 'required|string|max:50',
            'cast_in_slab' => 'boolean',
            'performance_solution' => 'boolean',
            'coordinates_x' => 'nullable|numeric',
            'coordinates_y' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verify work area belongs to project
        $workArea = $project->workAreas()->find($request->work_area_id);
        if (!$workArea) {
            return response()->json([
                'message' => 'Work area not found in this project'
            ], 422);
        }

        // Check if service_id already exists for this project
        if ($project->firePenetrations()->where('service_id', $request->service_id)->exists()) {
            return response()->json([
                'message' => 'Service ID already exists for this project'
            ], 422);
        }

        $penetration = $project->firePenetrations()->create([
            ...$request->validated(),
            'service_provider_id' => $user->id,
            'status' => 'not_started',
            'inspection_requested' => false,
        ]);

        return response()->json($penetration->load(['serviceProvider', 'workArea']), 201);
    }

    /**
     * Display the specified fire penetration.
     */
    public function show(FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanAccessPenetration($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($firePenetration->load([
            'serviceProvider',
            'inspector',
            'workArea.level',
            'project',
            'images',
            'comments.user'
        ]));
    }

    /**
     * Update the specified fire penetration.
     */
    public function update(Request $request, FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdatePenetration($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'service_name' => 'sometimes|string|max:255',
            'service_type' => 'sometimes|in:electrical,plumbing,dry_fire_services,hvac,wet_fire_services',
            'service_description' => 'sometimes|nullable|string',
            'tag' => 'sometimes|string|max:50',
            'team_contact' => 'sometimes|nullable|string|max:255',
            'substrate_type' => 'sometimes|in:wall,floor,ceiling',
            'substrate_material' => 'sometimes|in:concrete,hebel,steel,timber,plasterboard',
            'frl_rating' => 'sometimes|string|max:50',
            'cast_in_slab' => 'sometimes|boolean',
            'performance_solution' => 'sometimes|boolean',
            'coordinates_x' => 'sometimes|nullable|numeric',
            'coordinates_y' => 'sometimes|nullable|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $firePenetration->update($request->validated());

        return response()->json($firePenetration->load(['serviceProvider', 'workArea']));
    }

    /**
     * Remove the specified fire penetration.
     */
    public function destroy(FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdatePenetration($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete associated images
        $firePenetration->images()->each(function ($image) {
            if (Storage::exists($image->image_path)) {
                Storage::delete($image->image_path);
            }
            $image->delete();
        });

        $firePenetration->delete();

        return response()->json(['message' => 'Fire penetration deleted successfully']);
    }

    /**
     * Update penetration status.
     */
    public function updateStatus(Request $request, FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdatePenetrationStatus($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'required|in:not_started,labelled,images_uploaded,interim_pass,ready_for_inspection,amendments_required,final_pass',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $firePenetration->update([
            'status' => $request->status,
            'updated_at' => now(),
        ]);

        return response()->json($firePenetration);
    }

    /**
     * Request inspection for penetration.
     */
    public function requestInspection(FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdatePenetration($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        if ($firePenetration->status !== 'images_uploaded') {
            return response()->json([
                'message' => 'Penetration must have images uploaded before requesting inspection'
            ], 422);
        }

        $firePenetration->update([
            'inspection_requested' => true,
            'status' => 'ready_for_inspection',
            'updated_at' => now(),
        ]);

        return response()->json($firePenetration);
    }

    /**
     * Perform inspection on penetration.
     */
    public function performInspection(Request $request, FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if ($user->user_type !== 'inspector' && $user->user_type !== 'super_admin') {
            return response()->json(['message' => 'Only inspectors can perform inspections'], 403);
        }

        if (!$this->userCanAccessPenetration($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'result' => 'required|in:approved,amendments_required',
            'comments' => 'nullable|string',
            'amendments_notes' => 'required_if:result,amendments_required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $status = $request->result === 'approved' ? 'final_pass' : 'amendments_required';

        $firePenetration->update([
            'inspector_id' => $user->id,
            'status' => $status,
            'inspection_date' => now(),
            'updated_at' => now(),
        ]);

        // Add inspection comment if provided
        if ($request->comments || $request->amendments_notes) {
            $firePenetration->comments()->create([
                'user_id' => $user->id,
                'comment' => $request->comments ?? $request->amendments_notes,
                'comment_type' => $request->result === 'approved' ? 'inspection_approved' : 'amendments_required',
            ]);
        }

        return response()->json($firePenetration->load(['inspector', 'comments.user']));
    }

    /**
     * Upload images for penetration.
     */
    public function uploadImages(Request $request, FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdatePenetration($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'images' => 'required|array|max:10',
            'images.*' => 'required|image|mimes:jpeg,png,jpg,gif|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $uploadedImages = [];

        foreach ($request->file('images') as $image) {
            $path = $image->store('penetrations/' . $firePenetration->id, 'public');

            $penetrationImage = $firePenetration->images()->create([
                'image_path' => $path,
                'image_name' => $image->getClientOriginalName(),
                'image_size' => $image->getSize(),
                'uploaded_by' => $user->id,
            ]);

            $uploadedImages[] = $penetrationImage;
        }

        // Update penetration status if not already set
        if ($firePenetration->status === 'not_started' || $firePenetration->status === 'labelled') {
            $firePenetration->update(['status' => 'images_uploaded']);
        }

        return response()->json([
            'message' => 'Images uploaded successfully',
            'images' => $uploadedImages,
            'penetration' => $firePenetration->fresh()
        ]);
    }

    /**
     * Get images for penetration.
     */
    public function getImages(FirePenetration $firePenetration): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanAccessPenetration($user, $firePenetration)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $images = $firePenetration->images()->with('uploadedBy')->get();

        return response()->json($images);
    }

    /**
     * Check if user can access penetration.
     */
    private function userCanAccessPenetration($user, FirePenetration $penetration): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        // Check if user has access to the project
        $project = $penetration->project;
        return $project->hasMember($user);
    }

    /**
     * Check if user can update penetration.
     */
    private function userCanUpdatePenetration($user, FirePenetration $penetration): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        // Service providers can only update their own penetrations
        if ($user->user_type === 'service_provider') {
            return $penetration->service_provider_id === $user->id;
        }

        // Project admins can update any penetration in their projects
        $project = $penetration->project;
        if ($project->project_admin_id === $user->id) {
            return true;
        }

        $memberRole = $project->getMemberRole($user);
        return $memberRole === 'project_admin';
    }

    /**
     * Check if user can update penetration status.
     */
    private function userCanUpdatePenetrationStatus($user, FirePenetration $penetration): bool
    {
        // Service providers can update their own penetration status
        if ($user->user_type === 'service_provider' && $penetration->service_provider_id === $user->id) {
            return true;
        }

        // Inspectors can update status during inspection
        if ($user->user_type === 'inspector') {
            $project = $penetration->project;
            return $project->hasMember($user);
        }

        // Admins can always update status
        return $this->userCanUpdatePenetration($user, $penetration);
    }

    /**
     * Check if user can access project.
     */
    private function userCanAccessProject($user, Project $project): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        return $project->hasMember($user);
    }
}
