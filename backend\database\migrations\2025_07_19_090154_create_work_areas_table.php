<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_areas', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('level_id')->constrained('project_levels')->onDelete('cascade');
            $table->string('area_code', 50);
            $table->string('area_name');
            $table->enum('area_type', ['floor', 'wall', 'ceiling']);
            $table->enum('category', ['apartment', 'commercial', 'common_area', 'mechanical']);
            $table->string('frl_rating', 50)->nullable();
            $table->enum('substrate_type', ['wall', 'floor', 'ceiling']);
            $table->enum('substrate_material', ['concrete', 'hebel', 'steel', 'timber']);
            $table->json('polygon_coordinates');
            $table->string('display_color', 7)->default('#1976D2');
            $table->integer('line_weight')->default(2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_areas');
    }
};
