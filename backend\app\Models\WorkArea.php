<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class WorkArea extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'level_id',
        'area_code',
        'area_name',
        'area_type',
        'category',
        'frl_rating',
        'substrate_type',
        'substrate_material',
        'polygon_coordinates',
        'display_color',
        'line_weight',
    ];

    protected function casts(): array
    {
        return [
            'polygon_coordinates' => 'array',
        ];
    }

    /**
     * Get the project that owns the work area.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the level that owns the work area.
     */
    public function level()
    {
        return $this->belongsTo(ProjectLevel::class, 'level_id');
    }

    /**
     * Get fire penetrations in this work area.
     */
    public function firePenetrations()
    {
        return $this->hasMany(FirePenetration::class);
    }
}
