import { useAuth } from '@/contexts/AuthContext';

export interface Permission {
  action: string;
  resource: string;
  condition?: (user: any, context?: any) => boolean;
}

// Define all available permissions
export const PERMISSIONS = {
  // User Management
  MANAGE_ALL_USERS: { action: 'manage', resource: 'all_users' },
  VIEW_USERS: { action: 'view', resource: 'users' },
  CREATE_USERS: { action: 'create', resource: 'users' },
  UPDATE_USERS: { action: 'update', resource: 'users' },
  DELETE_USERS: { action: 'delete', resource: 'users' },
  
  // Project Management
  MANAGE_ALL_PROJECTS: { action: 'manage', resource: 'all_projects' },
  VIEW_PROJECTS: { action: 'view', resource: 'projects' },
  CREATE_PROJECTS: { action: 'create', resource: 'projects' },
  UPDATE_PROJECTS: { action: 'update', resource: 'projects' },
  DELETE_PROJECTS: { action: 'delete', resource: 'projects' },
  
  // Project Members
  MANAGE_PROJECT_MEMBERS: { action: 'manage', resource: 'project_members' },
  INVITE_MEMBERS: { action: 'invite', resource: 'project_members' },
  REMOVE_MEMBERS: { action: 'remove', resource: 'project_members' },
  
  // Fire Penetrations
  VIEW_PENETRATIONS: { action: 'view', resource: 'penetrations' },
  CREATE_PENETRATIONS: { action: 'create', resource: 'penetrations' },
  UPDATE_PENETRATIONS: { action: 'update', resource: 'penetrations' },
  DELETE_PENETRATIONS: { action: 'delete', resource: 'penetrations' },
  
  // Inspections
  PERFORM_INSPECTIONS: { action: 'perform', resource: 'inspections' },
  APPROVE_PENETRATIONS: { action: 'approve', resource: 'penetrations' },
  REQUEST_AMENDMENTS: { action: 'request_amendments', resource: 'penetrations' },
  
  // Work Areas
  MANAGE_WORK_AREAS: { action: 'manage', resource: 'work_areas' },
  VIEW_WORK_AREAS: { action: 'view', resource: 'work_areas' },
  
  // System Settings
  MANAGE_SYSTEM_SETTINGS: { action: 'manage', resource: 'system_settings' },
  VIEW_SYSTEM_ANALYTICS: { action: 'view', resource: 'system_analytics' },
  
  // Manufacturers
  MANAGE_MANUFACTURERS: { action: 'manage', resource: 'manufacturers' },
  MANAGE_FIRE_SYSTEMS: { action: 'manage', resource: 'fire_systems' },
  
  // File Management
  UPLOAD_FILES: { action: 'upload', resource: 'files' },
  DELETE_FILES: { action: 'delete', resource: 'files' },
} as const;

// Role-based permission mapping
const ROLE_PERMISSIONS: Record<string, Permission[]> = {
  super_admin: [
    PERMISSIONS.MANAGE_ALL_USERS,
    PERMISSIONS.MANAGE_ALL_PROJECTS,
    PERMISSIONS.MANAGE_SYSTEM_SETTINGS,
    PERMISSIONS.VIEW_SYSTEM_ANALYTICS,
    PERMISSIONS.MANAGE_MANUFACTURERS,
    PERMISSIONS.MANAGE_FIRE_SYSTEMS,
    PERMISSIONS.VIEW_PENETRATIONS,
    PERMISSIONS.CREATE_PENETRATIONS,
    PERMISSIONS.UPDATE_PENETRATIONS,
    PERMISSIONS.DELETE_PENETRATIONS,
    PERMISSIONS.PERFORM_INSPECTIONS,
    PERMISSIONS.APPROVE_PENETRATIONS,
    PERMISSIONS.REQUEST_AMENDMENTS,
    PERMISSIONS.MANAGE_WORK_AREAS,
    PERMISSIONS.UPLOAD_FILES,
    PERMISSIONS.DELETE_FILES,
  ],
  
  project_admin: [
    PERMISSIONS.VIEW_USERS,
    PERMISSIONS.CREATE_PROJECTS,
    PERMISSIONS.UPDATE_PROJECTS,
    PERMISSIONS.DELETE_PROJECTS,
    PERMISSIONS.MANAGE_PROJECT_MEMBERS,
    PERMISSIONS.INVITE_MEMBERS,
    PERMISSIONS.REMOVE_MEMBERS,
    PERMISSIONS.VIEW_PENETRATIONS,
    PERMISSIONS.CREATE_PENETRATIONS,
    PERMISSIONS.UPDATE_PENETRATIONS,
    PERMISSIONS.DELETE_PENETRATIONS,
    PERMISSIONS.MANAGE_WORK_AREAS,
    PERMISSIONS.UPLOAD_FILES,
    PERMISSIONS.DELETE_FILES,
  ],
  
  service_provider: [
    PERMISSIONS.VIEW_PROJECTS,
    PERMISSIONS.VIEW_PENETRATIONS,
    PERMISSIONS.CREATE_PENETRATIONS,
    {
      ...PERMISSIONS.UPDATE_PENETRATIONS,
      condition: (user, context) => {
        // Service providers can only update their own penetrations
        return context?.penetration?.service_provider_id === user.id;
      }
    },
    PERMISSIONS.VIEW_WORK_AREAS,
    PERMISSIONS.UPLOAD_FILES,
  ],
  
  inspector: [
    PERMISSIONS.VIEW_PROJECTS,
    PERMISSIONS.VIEW_PENETRATIONS,
    PERMISSIONS.PERFORM_INSPECTIONS,
    PERMISSIONS.APPROVE_PENETRATIONS,
    PERMISSIONS.REQUEST_AMENDMENTS,
    PERMISSIONS.VIEW_WORK_AREAS,
    PERMISSIONS.UPLOAD_FILES,
  ],
  
  guest: [
    PERMISSIONS.VIEW_PROJECTS,
    PERMISSIONS.VIEW_PENETRATIONS,
    PERMISSIONS.VIEW_WORK_AREAS,
  ],
};

export const usePermissions = () => {
  const { user } = useAuth();

  const hasPermission = (permission: Permission, context?: any): boolean => {
    if (!user) return false;

    const userPermissions = ROLE_PERMISSIONS[user.user_type] || [];
    
    const matchingPermission = userPermissions.find(
      p => p.action === permission.action && p.resource === permission.resource
    );

    if (!matchingPermission) return false;

    // Check condition if it exists
    if (matchingPermission.condition) {
      return matchingPermission.condition(user, context);
    }

    return true;
  };

  const hasAnyPermission = (permissions: Permission[], context?: any): boolean => {
    return permissions.some(permission => hasPermission(permission, context));
  };

  const hasAllPermissions = (permissions: Permission[], context?: any): boolean => {
    return permissions.every(permission => hasPermission(permission, context));
  };

  const canManageUsers = (): boolean => {
    return hasPermission(PERMISSIONS.MANAGE_ALL_USERS) || hasPermission(PERMISSIONS.VIEW_USERS);
  };

  const canManageProjects = (): boolean => {
    return hasPermission(PERMISSIONS.MANAGE_ALL_PROJECTS) || hasPermission(PERMISSIONS.CREATE_PROJECTS);
  };

  const canManageSystemSettings = (): boolean => {
    return hasPermission(PERMISSIONS.MANAGE_SYSTEM_SETTINGS);
  };

  const canPerformInspections = (): boolean => {
    return hasPermission(PERMISSIONS.PERFORM_INSPECTIONS);
  };

  const canManageManufacturers = (): boolean => {
    return hasPermission(PERMISSIONS.MANAGE_MANUFACTURERS);
  };

  const getUserPermissions = (): Permission[] => {
    if (!user) return [];
    return ROLE_PERMISSIONS[user.user_type] || [];
  };

  const isRole = (role: string): boolean => {
    return user?.user_type === role;
  };

  const isSuperAdmin = (): boolean => isRole('super_admin');
  const isProjectAdmin = (): boolean => isRole('project_admin');
  const isServiceProvider = (): boolean => isRole('service_provider');
  const isInspector = (): boolean => isRole('inspector');
  const isGuest = (): boolean => isRole('guest');

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canManageUsers,
    canManageProjects,
    canManageSystemSettings,
    canPerformInspections,
    canManageManufacturers,
    getUserPermissions,
    isRole,
    isSuperAdmin,
    isProjectAdmin,
    isServiceProvider,
    isInspector,
    isGuest,
    user,
  };
};

export default usePermissions;
