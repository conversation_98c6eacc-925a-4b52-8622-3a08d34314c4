import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import DashboardLayout from "@/components/DashboardLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { 
  ArrowLeft, 
  Users, 
  MapPin, 
  Calendar, 
  Building, 
  FileText, 
  Settings,
  Plus,
  Eye,
  Edit,
  Trash2,
  Upload,
  Download
} from "lucide-react";
import { usePermissions, PERMISSIONS } from "@/hooks/usePermissions";
import PermissionGate from "@/components/PermissionGate";
import { projectsApi } from "@/api/connectaBuildApi";
import { useToast } from "@/hooks/use-toast";

interface Project {
  id: number;
  project_code: string;
  project_name: string;
  description: string;
  address: string;
  city: string;
  state: string;
  postal_code: string;
  client_name: string;
  contractor_name: string;
  start_date: string;
  end_date: string;
  status: string;
  cover_image?: string;
}

interface ProjectMember {
  id: number;
  user: {
    id: number;
    first_name: string;
    last_name: string;
    email: string;
    company_name: string;
    discipline: string;
  };
  role: string;
  status: string;
}

interface WorkArea {
  id: number;
  area_code: string;
  area_name: string;
  area_type: string;
  category: string;
  frl_rating: string;
  penetrations_count: number;
  completed_penetrations: number;
}

const ProjectManagement = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { hasPermission, isSuperAdmin, isProjectAdmin } = usePermissions();
  
  const [project, setProject] = useState<Project | null>(null);
  const [members, setMembers] = useState<ProjectMember[]>([]);
  const [workAreas, setWorkAreas] = useState<WorkArea[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (projectId) {
      loadProjectData();
    }
  }, [projectId]);

  const loadProjectData = async () => {
    try {
      setLoading(true);
      
      // Load project details
      const projectData = await projectsApi.getProject(Number(projectId));
      setProject(projectData);
      
      // Load project members (mock data for now)
      setMembers([
        {
          id: 1,
          user: {
            id: 1,
            first_name: "John",
            last_name: "Smith",
            email: "<EMAIL>",
            company_name: "Spark Electrical",
            discipline: "electrical"
          },
          role: "service_provider",
          status: "active"
        },
        {
          id: 2,
          user: {
            id: 2,
            first_name: "Sarah",
            last_name: "Wilson",
            email: "<EMAIL>",
            company_name: "Quality Inspections",
            discipline: ""
          },
          role: "inspector",
          status: "active"
        }
      ]);
      
      // Load work areas
      const workAreasData = await projectsApi.getWorkAreas(Number(projectId));
      setWorkAreas(workAreasData || []);
      
    } catch (error: any) {
      console.error('Error loading project data:', error);
      toast({
        title: "Error",
        description: "Failed to load project data. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800';
      case 'draft': return 'bg-yellow-100 text-yellow-800';
      case 'completed': return 'bg-blue-100 text-blue-800';
      case 'archived': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDisciplineColor = (discipline: string) => {
    const colors: { [key: string]: string } = {
      electrical: "bg-yellow-100 text-yellow-800",
      plumbing: "bg-blue-100 text-blue-800",
      dry_fire_services: "bg-orange-100 text-orange-800",
      hvac: "bg-purple-100 text-purple-800",
      wet_fire_services: "bg-red-100 text-red-800"
    };
    return colors[discipline] || "bg-gray-100 text-gray-800";
  };

  const calculateProjectProgress = () => {
    if (workAreas.length === 0) return 0;
    const totalPenetrations = workAreas.reduce((sum, area) => sum + area.penetrations_count, 0);
    const completedPenetrations = workAreas.reduce((sum, area) => sum + area.completed_penetrations, 0);
    return totalPenetrations > 0 ? Math.round((completedPenetrations / totalPenetrations) * 100) : 0;
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-connecta-cyan"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!project) {
    return (
      <DashboardLayout>
        <div className="p-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-foreground mb-4">Project Not Found</h1>
            <Button onClick={() => navigate('/projects')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Projects
            </Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="p-8 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => navigate('/projects')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Projects
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-foreground">{project.project_name}</h1>
              <p className="text-muted-foreground">{project.project_code}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Badge className={getStatusColor(project.status)}>
              {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
            </Badge>
            <PermissionGate permission={PERMISSIONS.UPDATE_PROJECTS}>
              <Button variant="outline">
                <Edit className="w-4 h-4 mr-2" />
                Edit Project
              </Button>
            </PermissionGate>
          </div>
        </div>

        {/* Project Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Work Areas</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{workAreas.length}</div>
              <p className="text-xs text-muted-foreground">
                Defined areas
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Team Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{members.length}</div>
              <p className="text-xs text-muted-foreground">
                Active members
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Progress</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{calculateProjectProgress()}%</div>
              <p className="text-xs text-muted-foreground">
                Completion rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Duration</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.ceil((new Date(project.end_date).getTime() - new Date(project.start_date).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <p className="text-xs text-muted-foreground">
                Days total
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Progress Bar */}
        <Card>
          <CardHeader>
            <CardTitle>Project Progress</CardTitle>
            <CardDescription>Overall completion status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Overall Progress</span>
                <span>{calculateProjectProgress()}%</span>
              </div>
              <Progress value={calculateProjectProgress()} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="work-areas">Work Areas</TabsTrigger>
            <TabsTrigger value="team">Team</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Project Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Address</label>
                    <p className="text-sm">{project.address}, {project.city}, {project.state} {project.postal_code}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Client</label>
                    <p className="text-sm">{project.client_name || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Contractor</label>
                    <p className="text-sm">{project.contractor_name || 'Not specified'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Timeline</label>
                    <p className="text-sm">
                      {new Date(project.start_date).toLocaleDateString()} - {new Date(project.end_date).toLocaleDateString()}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Recent Activity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Work area WA-001 completed</p>
                        <p className="text-xs text-muted-foreground">2 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">New team member added</p>
                        <p className="text-xs text-muted-foreground">1 day ago</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">Inspection scheduled</p>
                        <p className="text-xs text-muted-foreground">2 days ago</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="work-areas" className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Work Areas</h3>
              <PermissionGate permission={PERMISSIONS.MANAGE_WORK_AREAS}>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Work Area
                </Button>
              </PermissionGate>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {workAreas.map((area) => (
                <Card key={area.id}>
                  <CardHeader>
                    <CardTitle className="text-base">{area.area_name}</CardTitle>
                    <CardDescription>{area.area_code}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Type:</span>
                        <Badge variant="outline">{area.area_type}</Badge>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Category:</span>
                        <span className="capitalize">{area.category}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>FRL Rating:</span>
                        <span>{area.frl_rating}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Penetrations:</span>
                        <span>{area.completed_penetrations}/{area.penetrations_count}</span>
                      </div>
                      <Progress 
                        value={area.penetrations_count > 0 ? (area.completed_penetrations / area.penetrations_count) * 100 : 0} 
                        className="h-2 mt-2" 
                      />
                    </div>
                    <div className="flex justify-end space-x-2 mt-4">
                      <Button variant="ghost" size="sm">
                        <Eye className="w-4 h-4" />
                      </Button>
                      <PermissionGate permission={PERMISSIONS.MANAGE_WORK_AREAS}>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button variant="ghost" size="sm" className="text-red-600">
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </PermissionGate>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="team" className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Team Members</h3>
              <PermissionGate permission={PERMISSIONS.INVITE_MEMBERS}>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Invite Member
                </Button>
              </PermissionGate>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {members.map((member) => (
                <Card key={member.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold">{member.user.first_name} {member.user.last_name}</h4>
                        <p className="text-sm text-muted-foreground">{member.user.email}</p>
                        <p className="text-sm text-muted-foreground">{member.user.company_name}</p>
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge variant="outline" className="capitalize">
                            {member.role.replace('_', ' ')}
                          </Badge>
                          {member.user.discipline && (
                            <Badge className={getDisciplineColor(member.user.discipline)}>
                              {member.user.discipline.replace('_', ' ')}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-2">
                        <Badge className={member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                          {member.status}
                        </Badge>
                        <PermissionGate permission={PERMISSIONS.MANAGE_PROJECT_MEMBERS}>
                          <div className="flex space-x-1">
                            <Button variant="ghost" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button variant="ghost" size="sm" className="text-red-600">
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </div>
                        </PermissionGate>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-semibold">Project Documents</h3>
              <PermissionGate permission={PERMISSIONS.UPLOAD_FILES}>
                <Button>
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Document
                </Button>
              </PermissionGate>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h4 className="text-lg font-semibold mb-2">No Documents Yet</h4>
                  <p className="text-muted-foreground mb-4">Upload project documents, plans, and reports here.</p>
                  <PermissionGate permission={PERMISSIONS.UPLOAD_FILES}>
                    <Button>
                      <Upload className="w-4 h-4 mr-2" />
                      Upload First Document
                    </Button>
                  </PermissionGate>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <PermissionGate 
              permission={PERMISSIONS.UPDATE_PROJECTS}
              fallback={
                <Card>
                  <CardContent className="p-6">
                    <div className="text-center">
                      <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h4 className="text-lg font-semibold mb-2">Access Restricted</h4>
                      <p className="text-muted-foreground">You don't have permission to modify project settings.</p>
                    </div>
                  </CardContent>
                </Card>
              }
            >
              <div className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Project Settings</CardTitle>
                    <CardDescription>Manage project configuration and preferences</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Project Status</label>
                        <select className="w-full mt-1 p-2 border rounded-md">
                          <option value="draft">Draft</option>
                          <option value="published">Published</option>
                          <option value="completed">Completed</option>
                          <option value="archived">Archived</option>
                        </select>
                      </div>
                      <div className="flex justify-end space-x-2">
                        <Button variant="outline">Cancel</Button>
                        <Button>Save Changes</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-red-600">Danger Zone</CardTitle>
                    <CardDescription>Irreversible and destructive actions</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center p-4 border border-red-200 rounded-lg">
                        <div>
                          <h4 className="font-semibold text-red-600">Delete Project</h4>
                          <p className="text-sm text-muted-foreground">Permanently delete this project and all associated data</p>
                        </div>
                        <Button variant="destructive">Delete Project</Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </PermissionGate>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default ProjectManagement;
