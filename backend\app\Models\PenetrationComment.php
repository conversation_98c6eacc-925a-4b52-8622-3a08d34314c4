<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PenetrationComment extends Model
{
    use HasFactory;

    protected $fillable = [
        'penetration_id',
        'user_id',
        'comment',
    ];

    /**
     * Get the penetration that owns the comment.
     */
    public function penetration()
    {
        return $this->belongsTo(FirePenetration::class, 'penetration_id');
    }

    /**
     * Get the user who made the comment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
