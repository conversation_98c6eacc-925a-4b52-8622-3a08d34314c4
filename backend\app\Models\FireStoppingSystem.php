<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FireStoppingSystem extends Model
{
    use HasFactory;

    protected $fillable = [
        'matrix_id',
        'manufacturer_id',
        'system_name',
        'service_types',
        'frl_rating',
    ];

    protected function casts(): array
    {
        return [
            'service_types' => 'array',
        ];
    }

    /**
     * Get the manufacturer that owns the system.
     */
    public function manufacturer()
    {
        return $this->belongsTo(Manufacturer::class);
    }

    /**
     * Get fire penetrations using this system.
     */
    public function firePenetrations()
    {
        return $this->hasMany(FirePenetration::class);
    }
}
