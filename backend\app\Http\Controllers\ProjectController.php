<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Project;
use App\Models\ProjectLevel;
use App\Models\WorkArea;
use App\Models\User;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 15);

        $query = Project::with(['admin', 'members']);

        // Filter based on user role
        if ($user->user_type === 'super_admin') {
            // Super admin can see all projects
        } elseif ($user->user_type === 'project_admin') {
            // Project admin can see projects they admin or are members of
            $query->where(function ($q) use ($user) {
                $q->where('project_admin_id', $user->id)
                  ->orWhereHas('members', function ($memberQuery) use ($user) {
                      $memberQuery->where('user_id', $user->id);
                  });
            });
        } else {
            // Service providers and inspectors can only see projects they're members of
            $query->whereHas('members', function ($memberQuery) use ($user) {
                $memberQuery->where('user_id', $user->id);
            });
        }

        $projects = $query->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'data' => $projects->items(),
            'current_page' => $projects->currentPage(),
            'last_page' => $projects->lastPage(),
            'per_page' => $projects->perPage(),
            'total' => $projects->total(),
        ]);
    }

    /**
     * Store a newly created project.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'project_code' => 'required|string|unique:projects',
            'project_name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'address' => 'required|string',
            'city' => 'required|string',
            'state' => 'required|string',
            'postal_code' => 'required|string',
            'country' => 'required|string',
            'client_name' => 'nullable|string',
            'client_email' => 'nullable|email',
            'client_phone' => 'nullable|string',
            'contractor_name' => 'nullable|string',
            'contractor_email' => 'nullable|email',
            'contractor_phone' => 'nullable|string',
            'cover_image' => 'nullable|string',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'status' => 'required|in:draft,published,completed,archived',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $project = Project::create([
            ...$request->validated(),
            'project_admin_id' => Auth::id(),
        ]);

        return response()->json($project->load(['admin', 'members']), 201);
    }

    /**
     * Display the specified project.
     */
    public function show(Project $project): JsonResponse
    {
        $user = Auth::user();

        // Check if user has access to this project
        if (!$this->userCanAccessProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        return response()->json($project->load([
            'admin',
            'members',
            'levels',
            'workAreas',
            'firePenetrations' => function ($query) {
                $query->with(['serviceProvider', 'inspector', 'workArea']);
            }
        ]));
    }

    /**
     * Update the specified project.
     */
    public function update(Request $request, Project $project): JsonResponse
    {
        $user = Auth::user();

        // Check if user can update this project
        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'project_name' => 'sometimes|string|max:255',
            'description' => 'sometimes|nullable|string',
            'address' => 'sometimes|string',
            'city' => 'sometimes|string',
            'state' => 'sometimes|string',
            'postal_code' => 'sometimes|string',
            'country' => 'sometimes|string',
            'client_name' => 'sometimes|nullable|string',
            'client_email' => 'sometimes|nullable|email',
            'client_phone' => 'sometimes|nullable|string',
            'contractor_name' => 'sometimes|nullable|string',
            'contractor_email' => 'sometimes|nullable|email',
            'contractor_phone' => 'sometimes|nullable|string',
            'cover_image' => 'sometimes|nullable|string',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after:start_date',
            'status' => 'sometimes|in:draft,published,completed,archived',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $project->update($request->validated());

        return response()->json($project->load(['admin', 'members']));
    }

    /**
     * Remove the specified project.
     */
    public function destroy(Project $project): JsonResponse
    {
        $user = Auth::user();

        // Only super admin or project admin can delete
        if ($user->user_type !== 'super_admin' && $project->project_admin_id !== $user->id) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $project->delete();

        return response()->json(['message' => 'Project deleted successfully']);
    }

    /**
     * Get all projects (Super Admin only).
     */
    public function getAllProjects(Request $request): JsonResponse
    {
        $page = $request->get('page', 1);
        $limit = $request->get('limit', 15);

        $projects = Project::with(['admin', 'members'])
            ->paginate($limit, ['*'], 'page', $page);

        return response()->json([
            'data' => $projects->items(),
            'current_page' => $projects->currentPage(),
            'last_page' => $projects->lastPage(),
            'per_page' => $projects->perPage(),
            'total' => $projects->total(),
        ]);
    }

    /**
     * Add member to project.
     */
    public function addMember(Request $request, Project $project): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'role' => 'required|in:service_provider,inspector,project_admin',
            'discipline' => 'nullable|string',
            'status' => 'required|in:active,pending,inactive',
            'permissions' => 'nullable|array',
            'assigned_work_areas' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $memberData = $request->validated();
        $project->members()->attach($memberData['user_id'], [
            'role' => $memberData['role'],
            'discipline' => $memberData['discipline'] ?? null,
            'status' => $memberData['status'],
            'permissions' => json_encode($memberData['permissions'] ?? []),
            'assigned_work_areas' => json_encode($memberData['assigned_work_areas'] ?? []),
        ]);

        return response()->json([
            'message' => 'Member added successfully',
            'member' => $project->members()->where('user_id', $memberData['user_id'])->first()
        ]);
    }

    /**
     * Remove member from project.
     */
    public function removeMember(Project $project, User $user): JsonResponse
    {
        $currentUser = Auth::user();

        if (!$this->userCanUpdateProject($currentUser, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $project->members()->detach($user->id);

        return response()->json(['message' => 'Member removed successfully']);
    }

    /**
     * Update project member.
     */
    public function updateMember(Request $request, Project $project, User $user): JsonResponse
    {
        $currentUser = Auth::user();

        if (!$this->userCanUpdateProject($currentUser, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'role' => 'sometimes|in:service_provider,inspector,project_admin',
            'discipline' => 'sometimes|nullable|string',
            'status' => 'sometimes|in:active,pending,inactive',
            'permissions' => 'sometimes|nullable|array',
            'assigned_work_areas' => 'sometimes|nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $updateData = [];
        if ($request->has('role')) $updateData['role'] = $request->role;
        if ($request->has('discipline')) $updateData['discipline'] = $request->discipline;
        if ($request->has('status')) $updateData['status'] = $request->status;
        if ($request->has('permissions')) $updateData['permissions'] = json_encode($request->permissions);
        if ($request->has('assigned_work_areas')) $updateData['assigned_work_areas'] = json_encode($request->assigned_work_areas);

        $project->members()->updateExistingPivot($user->id, $updateData);

        return response()->json([
            'message' => 'Member updated successfully',
            'member' => $project->members()->where('user_id', $user->id)->first()
        ]);
    }

    /**
     * Check if user can access project.
     */
    private function userCanAccessProject(User $user, Project $project): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        return $project->hasMember($user);
    }

    /**
     * Check if user can update project.
     */
    private function userCanUpdateProject(User $user, Project $project): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        if ($project->project_admin_id === $user->id) {
            return true;
        }

        // Check if user is a project admin member
        $memberRole = $project->getMemberRole($user);
        return $memberRole === 'project_admin';
    }
}
