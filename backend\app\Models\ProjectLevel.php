<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ProjectLevel extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'level_name',
        'level_number',
        'level_type',
    ];

    /**
     * Get the project that owns the level.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get work areas for this level.
     */
    public function workAreas()
    {
        return $this->hasMany(WorkArea::class, 'level_id');
    }

    /**
     * Get plan overlays for this level.
     */
    public function planOverlays()
    {
        return $this->hasMany(PlanOverlay::class, 'level_id');
    }
}
