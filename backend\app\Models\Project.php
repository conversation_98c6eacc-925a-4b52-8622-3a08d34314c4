<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_code',
        'project_name',
        'description',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'project_admin_id',
        'client_name',
        'client_email',
        'client_phone',
        'contractor_name',
        'contractor_email',
        'contractor_phone',
        'cover_image',
        'start_date',
        'end_date',
        'status',
    ];

    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'end_date' => 'date',
        ];
    }

    /**
     * Get the project admin.
     */
    public function admin()
    {
        return $this->belongsTo(User::class, 'project_admin_id');
    }

    /**
     * Get project members.
     */
    public function members()
    {
        return $this->belongsToMany(User::class, 'project_members')
                    ->withPivot(['role', 'discipline', 'status', 'permissions', 'assigned_work_areas'])
                    ->withTimestamps();
    }

    /**
     * Get project levels.
     */
    public function levels()
    {
        return $this->hasMany(ProjectLevel::class);
    }

    /**
     * Get work areas.
     */
    public function workAreas()
    {
        return $this->hasMany(WorkArea::class);
    }

    /**
     * Get fire penetrations.
     */
    public function firePenetrations()
    {
        return $this->hasMany(FirePenetration::class);
    }

    /**
     * Get plan overlays.
     */
    public function planOverlays()
    {
        return $this->hasMany(PlanOverlay::class);
    }

    /**
     * Get project invitations.
     */
    public function invitations()
    {
        return $this->hasMany(ProjectInvitation::class);
    }

    /**
     * Check if user is member of this project.
     */
    public function hasMember(User $user): bool
    {
        return $this->members()->where('user_id', $user->id)->exists() ||
               $this->project_admin_id === $user->id;
    }

    /**
     * Get member role in project.
     */
    public function getMemberRole(User $user): ?string
    {
        if ($this->project_admin_id === $user->id) {
            return 'project_admin';
        }

        $member = $this->members()->where('user_id', $user->id)->first();
        return $member ? $member->pivot->role : null;
    }
}
