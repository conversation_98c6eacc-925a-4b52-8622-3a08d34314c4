import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Plus, Eye, Edit } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAppDispatch } from "@/hooks/useAppDispatch";
import { useAppSelector } from "@/hooks/useAppSelector";
import { fetchUsers, createUser, updateUser } from "@/store/slices/usersSlice";

const ManageUsers = () => {
  const dispatch = useAppDispatch();
  const { users, loading } = useAppSelector((state) => state.users);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingUser, setEditingUser] = useState<any>(null);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [isViewUserOpen, setIsViewUserOpen] = useState(false);
  const [successModalOpen, setSuccessModalOpen] = useState(false);
  const [newUserForm, setNewUserForm] = useState({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
    phone: "",
    userType: "service_provider",
    discipline: "",
    status: "active"
  });
  const { toast } = useToast();

  useEffect(() => {
    dispatch(fetchUsers());
  }, [dispatch]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 hover:bg-green-100';
      case 'inactive':
        return 'bg-red-100 text-red-800 hover:bg-red-100';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-100';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-100';
    }
  };

  const filteredUsers = users.filter(user =>
    `${user.first_name} ${user.last_name}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.company_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSaveUser = async () => {
    try {
      if (isAddUserOpen) {
        const userData = {
          first_name: newUserForm.firstName,
          last_name: newUserForm.lastName,
          email: newUserForm.email,
          password: 'temp123', // Temporary password
          company_name: newUserForm.company,
          discipline: newUserForm.discipline.toLowerCase() as any,
          user_type: 'service_provider' as const,
          status: 'active' as const,
          phone: '',
          date_joined: new Date().toISOString(),
          last_login: null
        };
        
        await dispatch(createUser(userData));
        setNewUserForm({
          firstName: "",
          lastName: "",
          email: "",
          company: "",
          phone: "",
          userType: "service_provider",
          discipline: "",
          status: "active"
        });
      } else if (isEditUserOpen && editingUser) {
        const updatedData = {
          first_name: editingUser.first_name,
          last_name: editingUser.last_name,
          email: editingUser.email,
          company_name: editingUser.company_name,
          discipline: editingUser.discipline
        };
        
        await dispatch(updateUser({ id: editingUser.id, userData: updatedData }));
      }
      
      setIsEditUserOpen(false);
      setIsAddUserOpen(false);
      setSuccessModalOpen(true);
      
      toast({
        title: "Success",
        description: isAddUserOpen ? "User created successfully" : "User updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save user. Please try again.",
        variant: "destructive",
      });
    }
  };

  const SuccessModal = () => (
    <Dialog open={successModalOpen} onOpenChange={setSuccessModalOpen}>
      <DialogContent className="max-w-sm text-center">
        <div className="flex flex-col items-center space-y-4 py-4">
          <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold">User successfully saved!</h3>
          <Button 
            onClick={() => setSuccessModalOpen(false)}
            className="bg-gray-800 hover:bg-gray-900 text-white px-8"
          >
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );

  const AddUserDialog = () => (
    <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
      <DialogContent className="max-w-md bg-white z-50">
        <DialogHeader>
          <DialogTitle>Add User</DialogTitle>
        </DialogHeader>
         <div className="space-y-4">
           <div>
             <Label htmlFor="firstName">First Name</Label>
             <Input 
               id="firstName" 
               placeholder="Enter first name"
               value={newUserForm.firstName}
               onChange={(e) => setNewUserForm(prev => ({ ...prev, firstName: e.target.value }))}
             />
           </div>
           <div>
             <Label htmlFor="lastName">Last Name</Label>
             <Input 
               id="lastName" 
               placeholder="Enter last name"
               value={newUserForm.lastName}
               onChange={(e) => setNewUserForm(prev => ({ ...prev, lastName: e.target.value }))}
             />
           </div>
           <div>
             <Label htmlFor="email">Email Address</Label>
             <Input 
               id="email" 
               type="email" 
               placeholder="Enter email address"
               value={newUserForm.email}
               onChange={(e) => setNewUserForm(prev => ({ ...prev, email: e.target.value }))}
             />
           </div>
           <div>
             <Label htmlFor="company">Company Name</Label>
             <Input
               id="company"
               placeholder="Enter company name"
               value={newUserForm.company}
               onChange={(e) => setNewUserForm(prev => ({ ...prev, company: e.target.value }))}
             />
           </div>
           <div>
             <Label htmlFor="phone">Phone Number</Label>
             <Input
               id="phone"
               placeholder="Enter phone number"
               value={newUserForm.phone}
               onChange={(e) => setNewUserForm(prev => ({ ...prev, phone: e.target.value }))}
             />
           </div>
           <div>
             <Label htmlFor="userType">User Type</Label>
             <Select value={newUserForm.userType} onValueChange={(value) => setNewUserForm(prev => ({ ...prev, userType: value }))}>
               <SelectTrigger className="bg-white">
                 <SelectValue placeholder="Select user type" />
               </SelectTrigger>
               <SelectContent className="bg-white z-50">
                 <SelectItem value="super_admin">Super Admin</SelectItem>
                 <SelectItem value="project_admin">Project Admin</SelectItem>
                 <SelectItem value="service_provider">Service Provider</SelectItem>
                 <SelectItem value="inspector">Inspector</SelectItem>
                 <SelectItem value="guest">Guest</SelectItem>
               </SelectContent>
             </Select>
           </div>
           <div>
             <Label htmlFor="discipline">Discipline</Label>
             <Select value={newUserForm.discipline} onValueChange={(value) => setNewUserForm(prev => ({ ...prev, discipline: value }))}>
               <SelectTrigger className="bg-white">
                 <SelectValue placeholder="Select discipline (optional)" />
               </SelectTrigger>
               <SelectContent className="bg-white z-50">
                 <SelectItem value="electrical">Electrical</SelectItem>
                 <SelectItem value="plumbing">Plumbing</SelectItem>
                 <SelectItem value="dry_fire_services">Dry Fire Services</SelectItem>
                 <SelectItem value="hvac">HVAC</SelectItem>
                 <SelectItem value="wet_fire_services">Wet Fire Services</SelectItem>
               </SelectContent>
             </Select>
           </div>
           <div>
             <Label htmlFor="status">Status</Label>
             <Select value={newUserForm.status} onValueChange={(value) => setNewUserForm(prev => ({ ...prev, status: value }))}>
               <SelectTrigger className="bg-white">
                 <SelectValue placeholder="Select status" />
               </SelectTrigger>
               <SelectContent className="bg-white z-50">
                 <SelectItem value="active">Active</SelectItem>
                 <SelectItem value="inactive">Inactive</SelectItem>
                 <SelectItem value="pending">Pending</SelectItem>
               </SelectContent>
             </Select>
           </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button 
              variant="outline" 
              onClick={() => setIsAddUserOpen(false)}
              className="px-8"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSaveUser}
              className="bg-gray-800 hover:bg-gray-900 text-white px-8"
            >
              Save
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  const EditUserDialog = ({ user }: { user: any }) => (
    <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
      <DialogContent className="max-w-md bg-white z-50">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <Label htmlFor="firstName">First Name</Label>
            <Input id="firstName" defaultValue={user?.name?.split(" ")[0] || ""} />
          </div>
          <div>
            <Label htmlFor="lastName">Last Name</Label>
            <Input id="lastName" defaultValue={user?.name?.split(" ")[1] || ""} />
          </div>
          <div>
            <Label htmlFor="email">Email Address</Label>
            <Input id="email" type="email" defaultValue={user?.email || ""} />
          </div>
          <div>
            <Label htmlFor="company">Company Name</Label>
            <Input id="company" defaultValue={user?.company || ""} />
          </div>
          <div>
            <Label htmlFor="discipline">Discipline</Label>
            <Select defaultValue={user?.discipline?.toLowerCase() || ""}>
              <SelectTrigger className="bg-white">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                <SelectItem value="plumbing">Plumbing</SelectItem>
                <SelectItem value="electrical">Electrical</SelectItem>
                <SelectItem value="construction">Construction</SelectItem>
                <SelectItem value="management">Management</SelectItem>
                <SelectItem value="inspection">Inspection</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button 
              variant="outline" 
              onClick={() => setIsEditUserOpen(false)}
              className="px-8"
            >
              Cancel
            </Button>
            <Button 
              onClick={handleSaveUser}
              className="bg-gray-800 hover:bg-gray-900 text-white px-8"
            >
              Save
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  const ViewUserDialog = ({ user }: { user: any }) => (
    <Dialog open={isViewUserOpen} onOpenChange={setIsViewUserOpen}>
      <DialogContent className="max-w-md bg-white z-50">
        <DialogHeader>
          <DialogTitle>View User</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label className="text-sm font-medium text-muted-foreground">First Name</Label>
              <p className="text-sm">{user?.name?.split(" ")[0] || ""}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Last Name</Label>
              <p className="text-sm">{user?.name?.split(" ")[1] || ""}</p>
            </div>
            <div className="col-span-2">
              <Label className="text-sm font-medium text-muted-foreground">Email</Label>
              <p className="text-sm">{user?.email || ""}</p>
            </div>
            <div className="col-span-2">
              <Label className="text-sm font-medium text-muted-foreground">Company</Label>
              <p className="text-sm">{user?.company || ""}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Job Title</Label>
              <p className="text-sm">{user?.jobTitle || ""}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-muted-foreground">Status</Label>
              <Badge 
                variant="secondary" 
                className={user?.status === "Active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
              >
                {user?.status || ""}
              </Badge>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-foreground">Manage Users</h2>
        <Button 
          onClick={() => setIsAddUserOpen(true)}
          className="bg-connecta-dark-navy hover:bg-connecta-navy text-white"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add User
        </Button>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search for a user"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Company</TableHead>
              <TableHead>User Type</TableHead>
              <TableHead>Discipline</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4">
                  Loading users...
                </TableCell>
              </TableRow>
            ) : filteredUsers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-4">
                  No users found
                </TableCell>
              </TableRow>
            ) : (
              filteredUsers.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">U{user.id.toString().padStart(3, '0')}</TableCell>
                  <TableCell>{user.first_name} {user.last_name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.company_name}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {user.user_type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {user.discipline ? (
                      <Badge variant="secondary" className="capitalize">
                        {user.discipline.replace('_', ' ')}
                      </Badge>
                    ) : (
                      <span className="text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="secondary"
                      className={getStatusColor(user.status)}
                    >
                      {user.status.charAt(0).toUpperCase() + user.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => {
                          setEditingUser(user);
                          setIsViewUserOpen(true);
                        }}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => {
                          setEditingUser(user);
                          setIsEditUserOpen(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="link" 
                        size="sm"
                        className="text-connecta-cyan hover:text-connecta-navy p-0"
                      >
                        Manage
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="flex justify-center space-x-2">
        <Button variant="outline" size="sm">1</Button>
        <Button variant="outline" size="sm">2</Button>
        <Button variant="outline" size="sm">3</Button>
        <Button variant="outline" size="sm">4</Button>
        <Button variant="outline" size="sm">5</Button>
      </div>

      {/* Dialogs */}
      <AddUserDialog />
      {editingUser && <EditUserDialog user={editingUser} />}
      {editingUser && <ViewUserDialog user={editingUser} />}
      <SuccessModal />
    </div>
  );
};

export default ManageUsers;