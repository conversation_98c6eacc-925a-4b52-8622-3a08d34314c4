import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "@/store";
import { AuthProvider } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";
import Index from "./pages/Index";
import Projects from "./pages/Projects";
import Settings from "./pages/Settings";
import LoginPage from "./pages/LoginPage";
import CreateProject from "./pages/CreateProject";
import CreateWorkArea from "./pages/CreateWorkArea";
import AdminDashboard from "./pages/AdminDashboard";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <Provider store={store}>
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <AuthProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Index />} />
              <Route path="/login" element={<LoginPage />} />

              {/* Super Admin Only Routes */}
              <Route
                path="/admin"
                element={
                  <ProtectedRoute allowedRoles={['super_admin']}>
                    <AdminDashboard />
                  </ProtectedRoute>
                }
              />

              {/* Project Admin and Service Provider Routes */}
              <Route
                path="/projects"
                element={
                  <ProtectedRoute allowedRoles={['super_admin', 'project_admin', 'service_provider', 'inspector']}>
                    <Projects />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/projects/create"
                element={
                  <ProtectedRoute allowedRoles={['super_admin', 'project_admin']}>
                    <CreateProject />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/projects/:projectId/work-areas/create"
                element={
                  <ProtectedRoute allowedRoles={['super_admin', 'project_admin']}>
                    <CreateWorkArea />
                  </ProtectedRoute>
                }
              />

              {/* Settings - All authenticated users */}
              <Route
                path="/settings"
                element={
                  <ProtectedRoute>
                    <Settings />
                  </ProtectedRoute>
                }
              />

              {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </AuthProvider>
      </TooltipProvider>
    </QueryClientProvider>
  </Provider>
);

export default App;
