import React from 'react';
import { usePermissions, Permission } from '@/hooks/usePermissions';

interface PermissionGateProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean;
  roles?: string[];
  context?: any;
  fallback?: React.ReactNode;
}

const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  roles = [],
  context,
  fallback = null,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions, isRole } = usePermissions();

  // Check role-based access
  if (roles.length > 0) {
    const hasRequiredRole = roles.some(role => isRole(role));
    if (!hasRequiredRole) {
      return <>{fallback}</>;
    }
  }

  // Check single permission
  if (permission) {
    const hasAccess = hasPermission(permission, context);
    if (!hasAccess) {
      return <>{fallback}</>;
    }
  }

  // Check multiple permissions
  if (permissions.length > 0) {
    const hasAccess = requireAll 
      ? hasAllPermissions(permissions, context)
      : hasAnyPermission(permissions, context);
    
    if (!hasAccess) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
};

export default PermissionGate;
