import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { 
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  Building, 
  Phone, 
  Mail, 
  User,
  Package
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface Manufacturer {
  id: number;
  name: string;
  contact_person: string;
  phone: string;
  email: string;
  systems_count?: number;
  created_at: string;
}

interface FireStoppingSystem {
  id: number;
  matrix_id: string;
  system_name: string;
  service_types: string[];
  frl_rating: string;
}

const ManageManufacturers = () => {
  const { toast } = useToast();
  const [manufacturers, setManufacturers] = useState<Manufacturer[]>([
    {
      id: 1,
      name: "Hilti Australia",
      contact_person: "David Wilson",
      phone: "1800-HILTI",
      email: "<EMAIL>",
      systems_count: 15,
      created_at: "2024-01-15"
    },
    {
      id: 2,
      name: "Nullifire",
      contact_person: "Emma Thompson",
      phone: "1300-NULL-FIRE",
      email: "<EMAIL>",
      systems_count: 8,
      created_at: "2024-02-20"
    }
  ]);

  const [fireStoppingSystems, setFireStoppingSystems] = useState<FireStoppingSystem[]>([
    {
      id: 1,
      matrix_id: "FS-101",
      system_name: "Hilti Firestop Foam CFS-F",
      service_types: ["electrical", "plumbing"],
      frl_rating: "120/120/120"
    },
    {
      id: 2,
      matrix_id: "FS-102",
      system_name: "Nullifire FS702 Intumescent Sealant",
      service_types: ["electrical", "hvac"],
      frl_rating: "90/90/90"
    }
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [isAddManufacturerOpen, setIsAddManufacturerOpen] = useState(false);
  const [isAddSystemOpen, setIsAddSystemOpen] = useState(false);
  const [editingManufacturer, setEditingManufacturer] = useState<Manufacturer | null>(null);
  const [newManufacturer, setNewManufacturer] = useState({
    name: "",
    contact_person: "",
    phone: "",
    email: ""
  });
  const [newSystem, setNewSystem] = useState({
    matrix_id: "",
    manufacturer_id: "",
    system_name: "",
    service_types: [] as string[],
    frl_rating: ""
  });

  const filteredManufacturers = manufacturers.filter(manufacturer =>
    manufacturer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    manufacturer.contact_person.toLowerCase().includes(searchTerm.toLowerCase()) ||
    manufacturer.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSaveManufacturer = () => {
    if (editingManufacturer) {
      // Update existing manufacturer
      setManufacturers(prev => prev.map(m => 
        m.id === editingManufacturer.id 
          ? { ...editingManufacturer, ...newManufacturer }
          : m
      ));
      toast({
        title: "Manufacturer Updated",
        description: "Manufacturer information has been updated successfully.",
      });
    } else {
      // Add new manufacturer
      const newId = Math.max(...manufacturers.map(m => m.id)) + 1;
      setManufacturers(prev => [...prev, {
        id: newId,
        ...newManufacturer,
        systems_count: 0,
        created_at: new Date().toISOString().split('T')[0]
      }]);
      toast({
        title: "Manufacturer Added",
        description: "New manufacturer has been added successfully.",
      });
    }
    
    setNewManufacturer({ name: "", contact_person: "", phone: "", email: "" });
    setEditingManufacturer(null);
    setIsAddManufacturerOpen(false);
  };

  const handleDeleteManufacturer = (id: number) => {
    if (confirm("Are you sure you want to delete this manufacturer? This will also delete all associated fire stopping systems.")) {
      setManufacturers(prev => prev.filter(m => m.id !== id));
      toast({
        title: "Manufacturer Deleted",
        description: "Manufacturer has been deleted successfully.",
      });
    }
  };

  const handleSaveSystem = () => {
    const newId = Math.max(...fireStoppingSystems.map(s => s.id)) + 1;
    setFireStoppingSystems(prev => [...prev, {
      id: newId,
      ...newSystem,
      service_types: newSystem.service_types
    }]);
    
    toast({
      title: "Fire Stopping System Added",
      description: "New fire stopping system has been added successfully.",
    });
    
    setNewSystem({
      matrix_id: "",
      manufacturer_id: "",
      system_name: "",
      service_types: [],
      frl_rating: ""
    });
    setIsAddSystemOpen(false);
  };

  const getServiceTypeColor = (type: string) => {
    const colors: { [key: string]: string } = {
      electrical: "bg-yellow-100 text-yellow-800",
      plumbing: "bg-blue-100 text-blue-800",
      dry_fire_services: "bg-orange-100 text-orange-800",
      hvac: "bg-purple-100 text-purple-800",
      wet_fire_services: "bg-red-100 text-red-800"
    };
    return colors[type] || "bg-gray-100 text-gray-800";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Manufacturer Management</h1>
          <p className="text-muted-foreground">Manage manufacturers and fire stopping systems</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            onClick={() => setIsAddSystemOpen(true)}
            variant="outline"
          >
            <Package className="w-4 h-4 mr-2" />
            Add System
          </Button>
          <Button onClick={() => setIsAddManufacturerOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Add Manufacturer
          </Button>
        </div>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
        <Input
          placeholder="Search manufacturers..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Manufacturers Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="w-5 h-5 mr-2" />
            Manufacturers
          </CardTitle>
          <CardDescription>Manage fire stopping system manufacturers</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company Name</TableHead>
                <TableHead>Contact Person</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Systems</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredManufacturers.map((manufacturer) => (
                <TableRow key={manufacturer.id}>
                  <TableCell className="font-medium">{manufacturer.name}</TableCell>
                  <TableCell>{manufacturer.contact_person}</TableCell>
                  <TableCell>{manufacturer.phone}</TableCell>
                  <TableCell>{manufacturer.email}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {manufacturer.systems_count} systems
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => {
                          setEditingManufacturer(manufacturer);
                          setNewManufacturer({
                            name: manufacturer.name,
                            contact_person: manufacturer.contact_person,
                            phone: manufacturer.phone,
                            email: manufacturer.email
                          });
                          setIsAddManufacturerOpen(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleDeleteManufacturer(manufacturer.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Fire Stopping Systems */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="w-5 h-5 mr-2" />
            Fire Stopping Systems
          </CardTitle>
          <CardDescription>Manage fire stopping system products</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Matrix ID</TableHead>
                <TableHead>System Name</TableHead>
                <TableHead>Service Types</TableHead>
                <TableHead>FRL Rating</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {fireStoppingSystems.map((system) => (
                <TableRow key={system.id}>
                  <TableCell className="font-medium">{system.matrix_id}</TableCell>
                  <TableCell>{system.system_name}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {system.service_types.map((type) => (
                        <Badge 
                          key={type} 
                          variant="secondary" 
                          className={getServiceTypeColor(type)}
                        >
                          {type.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>{system.frl_rating}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Edit className="w-4 h-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Add/Edit Manufacturer Dialog */}
      <Dialog open={isAddManufacturerOpen} onOpenChange={setIsAddManufacturerOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>
              {editingManufacturer ? "Edit Manufacturer" : "Add New Manufacturer"}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Company Name</Label>
              <Input
                id="name"
                value={newManufacturer.name}
                onChange={(e) => setNewManufacturer(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter company name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="contact_person">Contact Person</Label>
              <Input
                id="contact_person"
                value={newManufacturer.contact_person}
                onChange={(e) => setNewManufacturer(prev => ({ ...prev, contact_person: e.target.value }))}
                placeholder="Enter contact person name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone</Label>
              <Input
                id="phone"
                value={newManufacturer.phone}
                onChange={(e) => setNewManufacturer(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="Enter phone number"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                value={newManufacturer.email}
                onChange={(e) => setNewManufacturer(prev => ({ ...prev, email: e.target.value }))}
                placeholder="Enter email address"
              />
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setIsAddManufacturerOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveManufacturer}>
                {editingManufacturer ? "Update" : "Add"} Manufacturer
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ManageManufacturers;
