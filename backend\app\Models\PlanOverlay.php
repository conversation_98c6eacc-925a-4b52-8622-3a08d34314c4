<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PlanOverlay extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'level_id',
        'overlay_type',
        'file_path',
        'is_base_plan',
        'uploaded_by',
    ];

    protected function casts(): array
    {
        return [
            'is_base_plan' => 'boolean',
        ];
    }

    /**
     * Get the project that owns the overlay.
     */
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the level that owns the overlay.
     */
    public function level()
    {
        return $this->belongsTo(ProjectLevel::class, 'level_id');
    }

    /**
     * Get the user who uploaded the overlay.
     */
    public function uploader()
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }
}
