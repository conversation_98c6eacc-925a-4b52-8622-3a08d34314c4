import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authApi = {
  login: async (email: string, password: string) => {
    const response = await api.post('/auth/login', { email, password });
    return response.data;
  },

  register: async (userData: any) => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  logout: async () => {
    const response = await api.post('/auth/logout');
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
    return response.data;
  },

  refresh: async () => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  getUserProfile: async () => {
    const response = await api.get('/auth/user-profile');
    return response.data;
  },
};

// Users API
export const usersApi = {
  getUsers: async (page = 1, limit = 15) => {
    const response = await api.get(`/users?page=${page}&limit=${limit}`);
    return response.data;
  },

  createUser: async (userData: any) => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  updateUser: async (userId: number, userData: any) => {
    const response = await api.put(`/users/${userId}`, userData);
    return response.data;
  },

  deleteUser: async (userId: number) => {
    const response = await api.delete(`/users/${userId}`);
    return response.data;
  },

  getUser: async (userId: number) => {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  },

  // Super Admin specific endpoints
  getAllUsers: async (page = 1, limit = 15) => {
    const response = await api.get(`/admin/users?page=${page}&limit=${limit}`);
    return response.data;
  },

  createUserAsAdmin: async (userData: any) => {
    const response = await api.post('/admin/users', userData);
    return response.data;
  },

  updateUserAsAdmin: async (userId: number, userData: any) => {
    const response = await api.put(`/admin/users/${userId}`, userData);
    return response.data;
  },

  deleteUserAsAdmin: async (userId: number) => {
    const response = await api.delete(`/admin/users/${userId}`);
    return response.data;
  },
};

// Projects API
export const projectsApi = {
  getProjects: async (page = 1, limit = 15) => {
    const response = await api.get(`/projects?page=${page}&limit=${limit}`);
    return response.data;
  },

  createProject: async (projectData: any) => {
    const response = await api.post('/projects', projectData);
    return response.data;
  },

  updateProject: async (projectId: number, projectData: any) => {
    const response = await api.put(`/projects/${projectId}`, projectData);
    return response.data;
  },

  deleteProject: async (projectId: number) => {
    const response = await api.delete(`/projects/${projectId}`);
    return response.data;
  },

  getProject: async (projectId: number) => {
    const response = await api.get(`/projects/${projectId}`);
    return response.data;
  },

  // Project members
  addMember: async (projectId: number, memberData: any) => {
    const response = await api.post(`/projects/${projectId}/members`, memberData);
    return response.data;
  },

  removeMember: async (projectId: number, userId: number) => {
    const response = await api.delete(`/projects/${projectId}/members/${userId}`);
    return response.data;
  },

  updateMember: async (projectId: number, userId: number, memberData: any) => {
    const response = await api.put(`/projects/${projectId}/members/${userId}`, memberData);
    return response.data;
  },

  // Work areas
  getWorkAreas: async (projectId: number) => {
    const response = await api.get(`/projects/${projectId}/work-areas`);
    return response.data;
  },

  createWorkArea: async (projectId: number, workAreaData: any) => {
    const response = await api.post(`/projects/${projectId}/work-areas`, workAreaData);
    return response.data;
  },

  updateWorkArea: async (workAreaId: number, workAreaData: any) => {
    const response = await api.put(`/work-areas/${workAreaId}`, workAreaData);
    return response.data;
  },

  deleteWorkArea: async (workAreaId: number) => {
    const response = await api.delete(`/work-areas/${workAreaId}`);
    return response.data;
  },

  // Project levels
  getLevels: async (projectId: number) => {
    const response = await api.get(`/projects/${projectId}/levels`);
    return response.data;
  },

  createLevel: async (projectId: number, levelData: any) => {
    const response = await api.post(`/projects/${projectId}/levels`, levelData);
    return response.data;
  },

  updateLevel: async (levelId: number, levelData: any) => {
    const response = await api.put(`/levels/${levelId}`, levelData);
    return response.data;
  },

  deleteLevel: async (levelId: number) => {
    const response = await api.delete(`/levels/${levelId}`);
    return response.data;
  },

  // Super Admin specific
  getAllProjects: async (page = 1, limit = 15) => {
    const response = await api.get(`/admin/projects?page=${page}&limit=${limit}`);
    return response.data;
  },
};

// Fire Penetrations API
export const firePenetrationsApi = {
  getPenetrations: async (page = 1, limit = 15) => {
    const response = await api.get(`/fire-penetrations?page=${page}&limit=${limit}`);
    return response.data;
  },

  createPenetration: async (penetrationData: any) => {
    const response = await api.post('/fire-penetrations', penetrationData);
    return response.data;
  },

  updatePenetration: async (penetrationId: number, penetrationData: any) => {
    const response = await api.put(`/fire-penetrations/${penetrationId}`, penetrationData);
    return response.data;
  },

  deletePenetration: async (penetrationId: number) => {
    const response = await api.delete(`/fire-penetrations/${penetrationId}`);
    return response.data;
  },

  uploadImage: async (penetrationId: number, imageData: FormData) => {
    const response = await api.post(`/fire-penetrations/${penetrationId}/images`, imageData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  addComment: async (penetrationId: number, comment: string) => {
    const response = await api.post(`/fire-penetrations/${penetrationId}/comments`, { comment });
    return response.data;
  },
};

// Manufacturers API
export const manufacturersApi = {
  getManufacturers: async () => {
    const response = await api.get('/manufacturers');
    return response.data;
  },

  createManufacturer: async (manufacturerData: any) => {
    const response = await api.post('/manufacturers', manufacturerData);
    return response.data;
  },

  getFireStoppingSystems: async () => {
    const response = await api.get('/fire-stopping-systems');
    return response.data;
  },

  createFireStoppingSystem: async (systemData: any) => {
    const response = await api.post('/fire-stopping-systems', systemData);
    return response.data;
  },
};

// Dashboard API
export const dashboardApi = {
  getStats: async () => {
    const response = await api.get('/dashboard/stats');
    return response.data;
  },

  getRecentActivity: async () => {
    const response = await api.get('/dashboard/recent-activity');
    return response.data;
  },

  getSystemAnalytics: async () => {
    const response = await api.get('/admin/analytics');
    return response.data;
  },
};

export default api;
