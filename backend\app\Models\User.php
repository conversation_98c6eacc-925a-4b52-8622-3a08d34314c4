<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Tymon\JWTAuth\Contracts\JWTSubject;

class User extends Authenticatable implements J<PERSON>TSubject
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'first_name',
        'last_name',
        'email',
        'password',
        'company_name',
        'phone',
        'user_type',
        'discipline',
        'status',
        'bio',
        'avatar_url',
        'notification_settings',
        'security_settings',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the identifier that will be stored in the subject claim of the JWT.
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Return a key value array, containing any custom claims to be added to the JWT.
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [];
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return "{$this->first_name} {$this->last_name}";
    }

    /**
     * Check if user is super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->user_type === 'super_admin';
    }

    /**
     * Check if user is project admin.
     */
    public function isProjectAdmin(): bool
    {
        return $this->user_type === 'project_admin';
    }

    /**
     * Check if user is service provider.
     */
    public function isServiceProvider(): bool
    {
        return $this->user_type === 'service_provider';
    }

    /**
     * Check if user is inspector.
     */
    public function isInspector(): bool
    {
        return $this->user_type === 'inspector';
    }

    /**
     * Projects where user is admin.
     */
    public function adminProjects()
    {
        return $this->hasMany(Project::class, 'project_admin_id');
    }

    /**
     * Projects where user is a member.
     */
    public function memberProjects()
    {
        return $this->belongsToMany(Project::class, 'project_members')
                    ->withPivot(['role', 'discipline', 'status', 'permissions', 'assigned_work_areas'])
                    ->withTimestamps();
    }

    /**
     * Fire penetrations assigned to this user as service provider.
     */
    public function servicePenetrations()
    {
        return $this->hasMany(FirePenetration::class, 'service_provider_id');
    }

    /**
     * Fire penetrations assigned to this user as inspector.
     */
    public function inspectionPenetrations()
    {
        return $this->hasMany(FirePenetration::class, 'inspector_id');
    }
}
