<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Models\Project;
use App\Models\WorkArea;

class WorkAreaController extends Controller
{
    /**
     * Display work areas for a project.
     */
    public function index(Project $project): JsonResponse
    {
        $user = Auth::user();

        // Check if user has access to this project
        if (!$this->userCanAccessProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $workAreas = $project->workAreas()
            ->with(['level', 'firePenetrations'])
            ->get()
            ->map(function ($workArea) {
                return [
                    'id' => $workArea->id,
                    'area_code' => $workArea->area_code,
                    'area_name' => $workArea->area_name,
                    'area_type' => $workArea->area_type,
                    'category' => $workArea->category,
                    'frl_rating' => $workArea->frl_rating,
                    'substrate_type' => $workArea->substrate_type,
                    'substrate_material' => $workArea->substrate_material,
                    'polygon_coordinates' => $workArea->polygon_coordinates,
                    'display_color' => $workArea->display_color,
                    'line_weight' => $workArea->line_weight,
                    'level_id' => $workArea->level_id,
                    'level' => $workArea->level,
                    'penetrations_count' => $workArea->firePenetrations->count(),
                    'completed_penetrations' => $workArea->firePenetrations->where('status', 'final_pass')->count(),
                    'created_at' => $workArea->created_at,
                    'updated_at' => $workArea->updated_at,
                ];
            });

        return response()->json($workAreas);
    }

    /**
     * Store a newly created work area.
     */
    public function store(Request $request, Project $project): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'level_id' => 'required|exists:project_levels,id',
            'area_code' => 'required|string|max:50',
            'area_name' => 'required|string|max:255',
            'area_type' => 'required|in:wall,floor,ceiling',
            'category' => 'required|in:apartment,commercial,common_area,mechanical,electrical,plumbing',
            'frl_rating' => 'required|string|max:50',
            'substrate_type' => 'required|in:wall,floor,ceiling',
            'substrate_material' => 'required|in:concrete,hebel,steel,timber,plasterboard',
            'polygon_coordinates' => 'nullable|array',
            'display_color' => 'nullable|string|max:7',
            'line_weight' => 'nullable|integer|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verify level belongs to project
        $level = $project->levels()->find($request->level_id);
        if (!$level) {
            return response()->json([
                'message' => 'Level not found in this project'
            ], 422);
        }

        // Check if area code already exists for this project
        if ($project->workAreas()->where('area_code', $request->area_code)->exists()) {
            return response()->json([
                'message' => 'Area code already exists for this project'
            ], 422);
        }

        $workArea = $project->workAreas()->create([
            ...$request->validated(),
            'polygon_coordinates' => json_encode($request->polygon_coordinates ?? []),
            'display_color' => $request->display_color ?? '#1976D2',
            'line_weight' => $request->line_weight ?? 2,
        ]);

        return response()->json($workArea->load('level'), 201);
    }

    /**
     * Display the specified work area.
     */
    public function show(Project $project, WorkArea $workArea): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanAccessProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Ensure work area belongs to project
        if ($workArea->project_id !== $project->id) {
            return response()->json(['message' => 'Work area not found'], 404);
        }

        return response()->json($workArea->load(['level', 'firePenetrations.serviceProvider', 'firePenetrations.inspector']));
    }

    /**
     * Update the specified work area.
     */
    public function update(Request $request, Project $project, WorkArea $workArea): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Ensure work area belongs to project
        if ($workArea->project_id !== $project->id) {
            return response()->json(['message' => 'Work area not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'level_id' => 'sometimes|exists:project_levels,id',
            'area_code' => 'sometimes|string|max:50',
            'area_name' => 'sometimes|string|max:255',
            'area_type' => 'sometimes|in:wall,floor,ceiling',
            'category' => 'sometimes|in:apartment,commercial,common_area,mechanical,electrical,plumbing',
            'frl_rating' => 'sometimes|string|max:50',
            'substrate_type' => 'sometimes|in:wall,floor,ceiling',
            'substrate_material' => 'sometimes|in:concrete,hebel,steel,timber,plasterboard',
            'polygon_coordinates' => 'sometimes|nullable|array',
            'display_color' => 'sometimes|nullable|string|max:7',
            'line_weight' => 'sometimes|nullable|integer|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verify level belongs to project if level_id is being updated
        if ($request->has('level_id')) {
            $level = $project->levels()->find($request->level_id);
            if (!$level) {
                return response()->json([
                    'message' => 'Level not found in this project'
                ], 422);
            }
        }

        // Check if area code already exists for this project (excluding current work area)
        if ($request->has('area_code')) {
            $existingArea = $project->workAreas()
                ->where('area_code', $request->area_code)
                ->where('id', '!=', $workArea->id)
                ->exists();

            if ($existingArea) {
                return response()->json([
                    'message' => 'Area code already exists for this project'
                ], 422);
            }
        }

        $updateData = $request->validated();
        if (isset($updateData['polygon_coordinates'])) {
            $updateData['polygon_coordinates'] = json_encode($updateData['polygon_coordinates']);
        }

        $workArea->update($updateData);

        return response()->json($workArea->load('level'));
    }

    /**
     * Remove the specified work area.
     */
    public function destroy(Project $project, WorkArea $workArea): JsonResponse
    {
        $user = Auth::user();

        if (!$this->userCanUpdateProject($user, $project)) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Ensure work area belongs to project
        if ($workArea->project_id !== $project->id) {
            return response()->json(['message' => 'Work area not found'], 404);
        }

        // Check if work area has fire penetrations
        if ($workArea->firePenetrations()->exists()) {
            return response()->json([
                'message' => 'Cannot delete work area with existing fire penetrations'
            ], 422);
        }

        $workArea->delete();

        return response()->json(['message' => 'Work area deleted successfully']);
    }

    /**
     * Check if user can access project.
     */
    private function userCanAccessProject($user, Project $project): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        return $project->hasMember($user);
    }

    /**
     * Check if user can update project.
     */
    private function userCanUpdateProject($user, Project $project): bool
    {
        if ($user->user_type === 'super_admin') {
            return true;
        }

        if ($project->project_admin_id === $user->id) {
            return true;
        }

        // Check if user is a project admin member
        $memberRole = $project->getMemberRole($user);
        return $memberRole === 'project_admin';
    }
}
