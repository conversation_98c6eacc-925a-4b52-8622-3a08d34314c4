<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fire_penetrations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->foreignId('work_area_id')->constrained('work_areas')->onDelete('cascade');
            $table->foreignId('service_provider_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('inspector_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('fire_stopping_system_id')->nullable()->constrained('fire_stopping_systems')->onDelete('set null');
            $table->string('service_id', 50);
            $table->string('service_name');
            $table->enum('service_type', ['electrical', 'plumbing', 'dry_fire_services', 'hvac', 'wet_fire_services']);
            $table->text('service_description')->nullable();
            $table->string('tag', 100)->nullable();
            $table->string('team_contact')->nullable();
            $table->enum('substrate_type', ['wall', 'floor', 'ceiling']);
            $table->enum('substrate_material', ['concrete', 'hebel', 'steel', 'timber']);
            $table->string('frl_rating', 50)->nullable();
            $table->boolean('cast_in_slab')->default(false);
            $table->boolean('performance_solution')->default(false);
            $table->boolean('inspection_requested')->default(false);
            $table->decimal('coordinates_x', 10, 6);
            $table->decimal('coordinates_y', 10, 6);
            $table->enum('status', ['not_started', 'labelled', 'images_uploaded', 'interim_pass', 'ready_for_inspection', 'amendments_required', 'final_pass'])->default('not_started');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fire_penetrations');
    }
};
